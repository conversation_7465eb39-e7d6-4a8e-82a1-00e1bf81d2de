"""
A.T.L.A.S Enhanced Market Data - Compatibility Bridge
This file provides backward compatibility by importing from the consolidated market data engine.
"""

import logging
import sys
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

# Note: Using fallback implementation as consolidated engine not available
logger.info("[BRIDGE] Using fallback enhanced market data implementation")

# Skip import attempt and go directly to fallback
# Fallback implementation for critical functionality
@dataclass
class MarketQuote:
        """Market quote data structure"""
        symbol: str
        price: float
        change: float
        change_percent: float
        volume: int
        timestamp: datetime
        bid: Optional[float] = None
        ask: Optional[float] = None
        high: Optional[float] = None
        low: Optional[float] = None
        open: Optional[float] = None

@dataclass
class HistoricalData:
    """Historical market data structure"""
    symbol: str
    timeframe: str
    data: List[Dict[str, Any]]
    start_date: datetime
    end_date: datetime

class MarketDataProvider:
        """Fallback market data provider"""
        
        def __init__(self):
            self.logger = logging.getLogger(__name__)
            self.logger.warning("[FALLBACK] Using fallback MarketDataProvider")
        
        async def get_quote(self, symbol: str) -> Optional[MarketQuote]:
            """Get quote for symbol"""
            self.logger.warning(f"[FALLBACK] Quote request for {symbol} - no data available")
            return MarketQuote(
                symbol=symbol,
                price=100.0,  # Default price
                change=0.0,
                change_percent=0.0,
                volume=1000000,
                timestamp=datetime.now()
            )
        
        async def get_historical_data(self, symbol: str, period: str = "1y") -> Optional[HistoricalData]:
            """Get historical data for symbol"""
            self.logger.warning(f"[FALLBACK] Historical data request for {symbol} - no data available")
            return HistoricalData(
                symbol=symbol,
                timeframe="1d",
                data=[],
                start_date=datetime.now(),
                end_date=datetime.now()
            )
        
        async def get_multiple_quotes(self, symbols: List[str]) -> Dict[str, MarketQuote]:
            """Get quotes for multiple symbols"""
            self.logger.warning(f"[FALLBACK] Multiple quotes request for {len(symbols)} symbols")
            quotes = {}
            for symbol in symbols:
                quotes[symbol] = await self.get_quote(symbol)
            return quotes

class EnhancedMarketDataEngine:
        """Fallback enhanced market data engine"""
        
        def __init__(self):
            self.logger = logging.getLogger(__name__)
            self.provider = MarketDataProvider()
            self.cache = {}
            self.logger.warning("[FALLBACK] Using fallback EnhancedMarketDataEngine")
        
        async def initialize(self):
            """Initialize the market data engine"""
            self.logger.info("[FALLBACK] Enhanced market data engine initialized in fallback mode")
            return True
        
        async def get_real_time_quote(self, symbol: str) -> Optional[MarketQuote]:
            """Get real-time quote"""
            return await self.provider.get_quote(symbol)
        
        async def get_historical_data(self, symbol: str, period: str = "1y") -> Optional[HistoricalData]:
            """Get historical data"""
            return await self.provider.get_historical_data(symbol, period)
        
        async def get_market_data_batch(self, symbols: List[str]) -> Dict[str, MarketQuote]:
            """Get market data for multiple symbols"""
            return await self.provider.get_multiple_quotes(symbols)
        
        def get_cached_quote(self, symbol: str) -> Optional[MarketQuote]:
            """Get cached quote if available"""
            return self.cache.get(symbol)
        
        def cache_quote(self, symbol: str, quote: MarketQuote):
            """Cache a quote"""
            self.cache[symbol] = quote
        
        async def start_real_time_feed(self, symbols: List[str]):
            """Start real-time data feed"""
            self.logger.info(f"[FALLBACK] Real-time feed requested for {len(symbols)} symbols")
        
        async def stop_real_time_feed(self):
            """Stop real-time data feed"""
            self.logger.info("[FALLBACK] Real-time feed stopped")
        
        def get_engine_status(self) -> Dict[str, Any]:
            """Get engine status"""
            return {
                "status": "fallback",
                "mode": "fallback",
                "cached_quotes": len(self.cache),
                "real_time_active": False
            }
    
# Create global instance
enhanced_market_data = EnhancedMarketDataEngine()

# Additional utility functions
async def get_quote(symbol: str) -> Optional[MarketQuote]:
    """Get quote for a symbol"""
    return await enhanced_market_data.get_real_time_quote(symbol)

async def get_quotes(symbols: List[str]) -> Dict[str, MarketQuote]:
    """Get quotes for multiple symbols"""
    return await enhanced_market_data.get_market_data_batch(symbols)

async def get_historical_data(symbol: str, period: str = "1y") -> Optional[HistoricalData]:
    """Get historical data for a symbol"""
    return await enhanced_market_data.get_historical_data(symbol, period)

def get_cached_quote(symbol: str) -> Optional[MarketQuote]:
    """Get cached quote if available"""
    return enhanced_market_data.get_cached_quote(symbol)

# Market data validation functions
def validate_symbol(symbol: str) -> bool:
    """Validate stock symbol format"""
    if not symbol or not isinstance(symbol, str):
        return False
    
    # Basic symbol validation
    symbol = symbol.upper().strip()
    if len(symbol) < 1 or len(symbol) > 10:
        return False
    
    # Check for valid characters (letters, numbers, some special chars)
    valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.-')
    return all(c in valid_chars for c in symbol)

def validate_quote_data(quote: MarketQuote) -> bool:
    """Validate quote data integrity"""
    try:
        # Check required fields
        if not quote.symbol or quote.price <= 0:
            return False
        
        # Check for reasonable values
        if quote.volume is not None and quote.volume < 0:
            return False
        
        # Check timestamp
        if not isinstance(quote.timestamp, datetime):
            return False
        
        return True
    except Exception:
        return False

# Market hours utilities
def is_market_open() -> bool:
    """Check if market is currently open (simplified)"""
    now = datetime.now()
    # Simplified check - market open weekdays 9:30 AM to 4:00 PM ET
    if now.weekday() >= 5:  # Weekend
        return False
    
    hour = now.hour
    return 9 <= hour < 16  # Simplified time check

def get_market_status() -> str:
    """Get current market status"""
    if is_market_open():
        return "OPEN"
    else:
        now = datetime.now()
        if now.weekday() >= 5:
            return "CLOSED_WEEKEND"
        else:
            hour = now.hour
            if hour < 9:
                return "PRE_MARKET"
            elif hour >= 16:
                return "AFTER_HOURS"
            else:
                return "CLOSED"

# Export main components for compatibility
__all__ = [
    'enhanced_market_data',
    'MarketQuote',
    'HistoricalData',
    'MarketDataProvider',
    'EnhancedMarketDataEngine',
    'get_quote',
    'get_quotes',
    'get_historical_data',
    'get_cached_quote',
    'validate_symbol',
    'validate_quote_data',
    'is_market_open',
    'get_market_status'
]
