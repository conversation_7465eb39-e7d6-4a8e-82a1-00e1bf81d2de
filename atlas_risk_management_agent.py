"""
A.T.L.A.S. Risk Management Agent
Advanced portfolio risk assessment and VaR calculations
"""

import logging
import math
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from atlas_multi_agent_core import (
    AtlasBaseAgent, AgentRole, AgentCapabilities, MultiAgentTask
)
from atlas_enhanced_market_data import AtlasEnhancedMarketDataEngine
from atlas_risk_core import AtlasRiskEngine

logger = logging.getLogger(__name__)

@dataclass
class RiskAssessment:
    """Risk assessment result"""
    symbol: str
    risk_score: float
    risk_level: str
    var_1day: float
    var_5day: float
    max_drawdown: float
    volatility: float
    beta: float
    sharpe_ratio: float
    risk_factors: List[str]
    recommendations: List[str]
    timestamp: datetime

class AtlasRiskManagementAgent(AtlasBaseAgent):
    """Risk management agent for portfolio risk assessment"""

    def __init__(self):
        super().__init__(AgentRole.RISK_MANAGER)
        self.risk_engine = None
        self.market_data_engine = None
        self.risk_thresholds = self._setup_risk_thresholds()

    def _define_capabilities(self) -> AgentCapabilities:
        """Define risk management agent capabilities"""
        return AgentCapabilities(
            role=AgentRole.RISK_MANAGER,
            supported_tasks=[
                "calculate_var",
                "assess_portfolio_risk",
                "position_sizing",
                "risk_monitoring",
                "stress_testing",
                "correlation_analysis"
            ],
            max_concurrent_tasks=3,
            average_processing_time=20.0,
            success_rate=0.92,
            dependencies=[AgentRole.DATA_VALIDATOR]
        )

    async def _initialize_agent(self):
        """Initialize the risk management agent"""
        try:
            # Initialize risk engine
            self.risk_engine = AtlasRiskEngine()
            await self.risk_engine.initialize()

            # Initialize market data engine
            self.market_data_engine = AtlasEnhancedMarketDataEngine()
            await self.market_data_engine.initialize()

            self.logger.info("Risk management agent initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize risk management agent: {e}")
            raise

    async def execute_task(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Execute a risk management task"""
        try:
            task_type = task.input_data.get("task_type", "assess_portfolio_risk")
            symbol = task.input_data.get("symbol", "")

            if task_type == "calculate_var":
                return await self._calculate_var(symbol, task.input_data)
            elif task_type == "assess_portfolio_risk":
                return await self._assess_portfolio_risk(symbol, task.input_data)
            elif task_type == "position_sizing":
                return await self._calculate_position_sizing(symbol, task.input_data)
            elif task_type == "risk_monitoring":
                return await self._monitor_risk(symbol, task.input_data)
            elif task_type == "stress_testing":
                return await self._perform_stress_test(symbol, task.input_data)
            elif task_type == "correlation_analysis":
                return await self._analyze_correlations(task.input_data)
            else:
                return {"error": f"Unknown task type: {task_type}"}

        except Exception as e:
            self.logger.error(f"Risk management task failed: {e}")
            self.failed_tasks += 1
            return {"error": str(e)}

    async def _calculate_var(self, symbol: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Value at Risk (VaR) for a position"""
        try:
            # Get market data for volatility calculation
            market_data = await self.market_data_engine.get_real_time_quote(symbol)

            if not market_data:
                return {"error": "No market data available", "symbol": symbol}

            # Get position parameters
            position_value = params.get("position_value", 10000)  # Default $10k position
            confidence_level = params.get("confidence_level", 0.95)  # 95% confidence
            time_horizon = params.get("time_horizon", 1)  # 1 day

            # Calculate historical volatility (simplified)
            volatility = await self._calculate_volatility(symbol)

            # Calculate VaR using parametric method
            z_score = self._get_z_score(confidence_level)
            var_amount = position_value * volatility * z_score * math.sqrt(time_horizon)

            # Calculate VaR percentage
            var_percentage = (var_amount / position_value) * 100

            # Risk assessment
            risk_level = self._assess_var_risk_level(var_percentage)

            self.completed_tasks += 1
            return {
                "symbol": symbol,
                "var_amount": var_amount,
                "var_percentage": var_percentage,
                "confidence_level": confidence_level,
                "time_horizon": time_horizon,
                "volatility": volatility,
                "risk_level": risk_level,
                "position_value": position_value,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"VaR calculation failed for {symbol}: {e}")
            return {"error": str(e), "symbol": symbol}

    async def _assess_portfolio_risk(self, symbol: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Assess comprehensive portfolio risk"""
        try:
            # Get market data
            market_data = await self.market_data_engine.get_real_time_quote(symbol)

            if not market_data:
                return {"error": "No market data available", "symbol": symbol}

            # Calculate risk metrics
            volatility = await self._calculate_volatility(symbol)
            beta = await self._calculate_beta(symbol)
            sharpe_ratio = await self._calculate_sharpe_ratio(symbol)
            max_drawdown = await self._calculate_max_drawdown(symbol)

            # Calculate VaR for different time horizons
            var_1day = await self._calculate_simple_var(symbol, 1, volatility)
            var_5day = await self._calculate_simple_var(symbol, 5, volatility)

            # Overall risk score (0-100)
            risk_score = self._calculate_composite_risk_score(
                volatility, beta, sharpe_ratio, max_drawdown
            )

            # Risk level classification
            risk_level = self._classify_risk_level(risk_score)

            # Identify risk factors
            risk_factors = self._identify_risk_factors(
                volatility, beta, sharpe_ratio, max_drawdown, market_data
            )

            # Generate recommendations
            recommendations = self._generate_risk_recommendations(
                risk_score, risk_factors, market_data
            )

            self.completed_tasks += 1
            return {
                "symbol": symbol,
                "risk_score": risk_score,
                "risk_level": risk_level,
                "metrics": {
                    "volatility": volatility,
                    "beta": beta,
                    "sharpe_ratio": sharpe_ratio,
                    "max_drawdown": max_drawdown,
                    "var_1day": var_1day,
                    "var_5day": var_5day
                },
                "risk_factors": risk_factors,
                "recommendations": recommendations,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Portfolio risk assessment failed for {symbol}: {e}")
            return {"error": str(e), "symbol": symbol}

    async def _calculate_position_sizing(self, symbol: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate optimal position sizing based on risk parameters"""
        try:
            # Get risk parameters
            account_value = params.get("account_value", 100000)  # $100k default
            risk_per_trade = params.get("risk_per_trade", 0.02)  # 2% risk per trade
            stop_loss_percent = params.get("stop_loss_percent", 0.05)  # 5% stop loss

            # Get current market data
            market_data = await self.market_data_engine.get_real_time_quote(symbol)

            if not market_data:
                return {"error": "No market data available", "symbol": symbol}

            current_price = market_data.get("price", 0)

            if current_price <= 0:
                return {"error": "Invalid price data", "symbol": symbol}

            # Calculate position size using risk-based method
            risk_amount = account_value * risk_per_trade
            stop_loss_amount = current_price * stop_loss_percent

            # Position size = Risk Amount / Stop Loss Amount
            position_size = risk_amount / stop_loss_amount if stop_loss_amount > 0 else 0

            # Calculate number of shares
            shares = int(position_size / current_price) if current_price > 0 else 0

            # Actual position value
            actual_position_value = shares * current_price

            # Actual risk amount
            actual_risk = shares * stop_loss_amount

            # Risk as percentage of account
            risk_percentage = (actual_risk / account_value) * 100

            return {
                "symbol": symbol,
                "recommended_shares": shares,
                "position_value": actual_position_value,
                "risk_amount": actual_risk,
                "risk_percentage": risk_percentage,
                "current_price": current_price,
                "stop_loss_price": current_price - stop_loss_amount,
                "account_utilization": (actual_position_value / account_value) * 100,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Position sizing calculation failed for {symbol}: {e}")
            return {"error": str(e), "symbol": symbol}

    async def _monitor_risk(self, symbol: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Monitor real-time risk metrics"""
        try:
            # Get current position info
            current_position = params.get("current_position", {})
            shares = current_position.get("shares", 0)
            entry_price = current_position.get("entry_price", 0)

            # Get current market data
            market_data = await self.market_data_engine.get_real_time_quote(symbol)

            if not market_data:
                return {"error": "No market data available", "symbol": symbol}

            current_price = market_data.get("price", 0)

            # Calculate current P&L
            unrealized_pnl = (current_price - entry_price) * shares
            unrealized_pnl_percent = ((current_price - entry_price) / entry_price) * 100 if entry_price > 0 else 0

            # Calculate current position value
            position_value = shares * current_price

            # Risk monitoring alerts
            alerts = []

            # Check for significant losses
            if unrealized_pnl_percent < -5:
                alerts.append({
                    "type": "loss_alert",
                    "message": f"Position down {unrealized_pnl_percent:.1f}%",
                    "severity": "high" if unrealized_pnl_percent < -10 else "medium"
                })

            # Check for high volatility
            volatility = await self._calculate_volatility(symbol)
            if volatility > 0.3:  # 30% annualized volatility
                alerts.append({
                    "type": "volatility_alert",
                    "message": f"High volatility detected: {volatility:.1%}",
                    "severity": "medium"
                })

            return {
                "symbol": symbol,
                "position_value": position_value,
                "unrealized_pnl": unrealized_pnl,
                "unrealized_pnl_percent": unrealized_pnl_percent,
                "current_price": current_price,
                "entry_price": entry_price,
                "shares": shares,
                "volatility": volatility,
                "alerts": alerts,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Risk monitoring failed for {symbol}: {e}")
            return {"error": str(e), "symbol": symbol}

    async def _perform_stress_test(self, symbol: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Perform stress testing on position"""
        try:
            # Get position parameters
            position_value = params.get("position_value", 10000)

            # Define stress scenarios
            scenarios = [
                {"name": "Market Crash", "price_change": -0.20},  # 20% drop
                {"name": "Moderate Decline", "price_change": -0.10},  # 10% drop
                {"name": "Minor Correction", "price_change": -0.05},  # 5% drop
                {"name": "Bull Run", "price_change": 0.15},  # 15% gain
                {"name": "Moderate Gain", "price_change": 0.08}  # 8% gain
            ]

            # Get current market data
            market_data = await self.market_data_engine.get_real_time_quote(symbol)

            if not market_data:
                return {"error": "No market data available", "symbol": symbol}

            current_price = market_data.get("price", 0)

            # Calculate stress test results
            stress_results = []
            for scenario in scenarios:
                new_price = current_price * (1 + scenario["price_change"])
                pnl = position_value * scenario["price_change"]
                pnl_percent = scenario["price_change"] * 100

                stress_results.append({
                    "scenario": scenario["name"],
                    "price_change": scenario["price_change"],
                    "new_price": new_price,
                    "pnl": pnl,
                    "pnl_percent": pnl_percent
                })

            # Calculate worst-case scenario
            worst_case = min(stress_results, key=lambda x: x["pnl"])
            best_case = max(stress_results, key=lambda x: x["pnl"])

            return {
                "symbol": symbol,
                "current_price": current_price,
                "position_value": position_value,
                "stress_scenarios": stress_results,
                "worst_case": worst_case,
                "best_case": best_case,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Stress testing failed for {symbol}: {e}")
            return {"error": str(e), "symbol": symbol}

    async def _analyze_correlations(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze correlations between positions"""
        try:
            symbols = params.get("symbols", [])

            if len(symbols) < 2:
                return {"error": "Need at least 2 symbols for correlation analysis"}

            # This would normally calculate correlations using historical data
            # For now, return a simplified correlation matrix
            correlations = {}

            for i, symbol1 in enumerate(symbols):
                correlations[symbol1] = {}
                for j, symbol2 in enumerate(symbols):
                    if i == j:
                        correlations[symbol1][symbol2] = 1.0
                    else:
                        # Simplified correlation (would use real historical data)
                        correlations[symbol1][symbol2] = 0.3  # Placeholder

            # Calculate portfolio diversification score
            avg_correlation = sum(
                correlations[s1][s2] for s1 in symbols for s2 in symbols if s1 != s2
            ) / (len(symbols) * (len(symbols) - 1))

            diversification_score = 1.0 - avg_correlation

            return {
                "correlation_matrix": correlations,
                "average_correlation": avg_correlation,
                "diversification_score": diversification_score,
                "diversification_rating": self._rate_diversification(diversification_score),
                "symbols": symbols,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Correlation analysis failed: {e}")
            return {"error": str(e)}

    def _setup_risk_thresholds(self) -> Dict[str, Any]:
        """Setup risk thresholds and limits"""
        return {
            "max_position_size": 0.10,  # 10% of portfolio
            "max_sector_exposure": 0.25,  # 25% in any sector
            "max_daily_var": 0.02,  # 2% daily VaR
            "min_sharpe_ratio": 0.5,
            "max_volatility": 0.40,  # 40% annualized
            "max_drawdown": 0.15  # 15% max drawdown
        }

    async def _calculate_volatility(self, symbol: str) -> float:
        """Calculate annualized volatility (simplified)"""
        try:
            # This would normally use historical price data
            # For now, using a simplified calculation based on current data
            market_data = await self.market_data_engine.get_real_time_quote(symbol)

            if not market_data:
                return 0.20  # Default 20% volatility

            # Use daily change as proxy for volatility
            daily_change = abs(market_data.get("change_percent", 2.0)) / 100.0

            # Annualize (252 trading days)
            annualized_volatility = daily_change * math.sqrt(252)

            return min(annualized_volatility, 1.0)  # Cap at 100%

        except Exception:
            return 0.20  # Default fallback

    async def _calculate_beta(self, symbol: str) -> float:
        """Calculate beta (simplified)"""
        # This would normally calculate beta vs market index
        # For now, return a reasonable default
        return 1.0

    async def _calculate_sharpe_ratio(self, symbol: str) -> float:
        """Calculate Sharpe ratio (simplified)"""
        # This would normally use historical returns and risk-free rate
        # For now, return a reasonable estimate
        return 0.8

    async def _calculate_max_drawdown(self, symbol: str) -> float:
        """Calculate maximum drawdown (simplified)"""
        # This would normally use historical price data
        # For now, return a reasonable estimate based on volatility
        volatility = await self._calculate_volatility(symbol)
        return min(volatility * 0.5, 0.30)  # Estimate based on volatility

    async def _calculate_simple_var(self, symbol: str, days: int, volatility: float) -> float:
        """Calculate simple VaR"""
        z_score = 1.645  # 95% confidence
        return volatility * z_score * math.sqrt(days) * 100  # Return as percentage

    def _get_z_score(self, confidence_level: float) -> float:
        """Get Z-score for confidence level"""
        z_scores = {
            0.90: 1.282,
            0.95: 1.645,
            0.99: 2.326
        }
        return z_scores.get(confidence_level, 1.645)

    def _assess_var_risk_level(self, var_percentage: float) -> str:
        """Assess VaR risk level"""
        if var_percentage > 10:
            return "very_high"
        elif var_percentage > 5:
            return "high"
        elif var_percentage > 2:
            return "medium"
        else:
            return "low"

    def _calculate_composite_risk_score(self, volatility: float, beta: float,
                                      sharpe_ratio: float, max_drawdown: float) -> float:
        """Calculate composite risk score (0-100)"""
        # Higher volatility = higher risk
        vol_score = min(volatility * 100, 50)

        # Higher beta = higher risk
        beta_score = min(abs(beta - 1.0) * 20, 20)

        # Lower Sharpe ratio = higher risk
        sharpe_score = max(20 - (sharpe_ratio * 20), 0)

        # Higher drawdown = higher risk
        drawdown_score = max_drawdown * 100

        composite = vol_score + beta_score + sharpe_score + drawdown_score
        return min(composite, 100)

    def _classify_risk_level(self, risk_score: float) -> str:
        """Classify risk level based on score"""
        if risk_score >= 80:
            return "very_high"
        elif risk_score >= 60:
            return "high"
        elif risk_score >= 40:
            return "medium"
        elif risk_score >= 20:
            return "low"
        else:
            return "very_low"

    def _identify_risk_factors(self, volatility: float, beta: float, sharpe_ratio: float,
                             max_drawdown: float, market_data: Dict[str, Any]) -> List[str]:
        """Identify specific risk factors"""
        factors = []

        if volatility > 0.30:
            factors.append("High volatility")

        if beta > 1.5:
            factors.append("High market sensitivity")
        elif beta < 0.5:
            factors.append("Low market correlation")

        if sharpe_ratio < 0.5:
            factors.append("Poor risk-adjusted returns")

        if max_drawdown > 0.20:
            factors.append("High maximum drawdown")

        # Check volume
        volume = market_data.get("volume", 0)
        if volume < 100000:  # Low volume threshold
            factors.append("Low trading volume")

        return factors

    def _generate_risk_recommendations(self, risk_score: float, risk_factors: List[str],
                                     market_data: Dict[str, Any]) -> List[str]:
        """Generate risk management recommendations"""
        recommendations = []

        if risk_score > 70:
            recommendations.append("Consider reducing position size")
            recommendations.append("Implement tight stop-loss orders")

        if "High volatility" in risk_factors:
            recommendations.append("Use smaller position sizes due to volatility")

        if "Low trading volume" in risk_factors:
            recommendations.append("Be cautious with large orders due to low liquidity")

        if "Poor risk-adjusted returns" in risk_factors:
            recommendations.append("Consider alternative investments with better risk/return profile")

        if risk_score < 30:
            recommendations.append("Low risk profile - suitable for larger positions")

        return recommendations

    def _rate_diversification(self, score: float) -> str:
        """Rate portfolio diversification"""
        if score >= 0.8:
            return "excellent"
        elif score >= 0.6:
            return "good"
        elif score >= 0.4:
            return "fair"
        elif score >= 0.2:
            return "poor"
        else:
            return "very_poor"

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = ["AtlasRiskManagementAgent", "RiskAssessment"]