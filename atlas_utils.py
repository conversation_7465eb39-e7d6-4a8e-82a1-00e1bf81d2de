"""
A.T.L.A.S. Advanced Utilities & Helper Functions
Comprehensive utility functions for the trading system
"""

import asyncio
import logging
import json
import hashlib
import time
import re
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Union, Callable, Tuple
from dataclasses import dataclass, asdict
from functools import wraps
from pathlib import Path
import inspect

logger = logging.getLogger(__name__)

# ============================================================================
# TIMING AND PERFORMANCE UTILITIES
# ============================================================================

def timing_decorator(func):
    """Decorator to measure function execution time"""
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.debug(f"⏱️ {func.__name__} executed in {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ {func.__name__} failed after {execution_time:.3f}s: {e}")
            raise
    
    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.debug(f"⏱️ {func.__name__} executed in {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ {func.__name__} failed after {execution_time:.3f}s: {e}")
            raise
    
    return async_wrapper if inspect.iscoroutinefunction(func) else sync_wrapper

class PerformanceMonitor:
    """Performance monitoring utility"""
    
    def __init__(self):
        self.metrics: Dict[str, List[float]] = {}
        self.start_times: Dict[str, float] = {}
    
    def start_timer(self, operation: str):
        """Start timing an operation"""
        self.start_times[operation] = time.time()
    
    def end_timer(self, operation: str) -> float:
        """End timing and record duration"""
        if operation not in self.start_times:
            return 0.0
        
        duration = time.time() - self.start_times[operation]
        
        if operation not in self.metrics:
            self.metrics[operation] = []
        
        self.metrics[operation].append(duration)
        del self.start_times[operation]
        
        return duration
    
    def get_stats(self, operation: str) -> Dict[str, float]:
        """Get performance statistics for an operation"""
        if operation not in self.metrics or not self.metrics[operation]:
            return {}
        
        durations = self.metrics[operation]
        return {
            "count": len(durations),
            "avg": sum(durations) / len(durations),
            "min": min(durations),
            "max": max(durations),
            "total": sum(durations)
        }

# ============================================================================
# DATA VALIDATION AND SANITIZATION
# ============================================================================

def validate_symbol(symbol: str) -> bool:
    """Validate stock symbol format"""
    if not symbol or not isinstance(symbol, str):
        return False
    
    # Remove whitespace and convert to uppercase
    symbol = symbol.strip().upper()
    
    # Check format: 1-5 uppercase letters
    return bool(re.match(r'^[A-Z]{1,5}$', symbol))

def sanitize_price(price: Union[str, int, float]) -> Optional[float]:
    """Sanitize and validate price input"""
    try:
        if isinstance(price, str):
            # Remove currency symbols and commas
            price = re.sub(r'[$,]', '', price.strip())
        
        price_float = float(price)
        
        # Validate range
        if price_float < 0 or price_float > 1000000:  # $1M max
            return None
        
        return round(price_float, 4)
        
    except (ValueError, TypeError):
        return None

def sanitize_volume(volume: Union[str, int, float]) -> Optional[int]:
    """Sanitize and validate volume input"""
    try:
        if isinstance(volume, str):
            # Remove commas
            volume = volume.replace(',', '').strip()
        
        volume_int = int(float(volume))
        
        # Validate range
        if volume_int < 0 or volume_int > 1000000000:  # 1B max
            return None
        
        return volume_int
        
    except (ValueError, TypeError):
        return None

def clean_text(text: str, max_length: int = 1000) -> str:
    """Clean and sanitize text input"""
    if not text:
        return ""
    
    # Remove dangerous patterns
    dangerous_patterns = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'on\w+\s*=',
        r'eval\s*\(',
        r'exec\s*\('
    ]
    
    cleaned = str(text)
    for pattern in dangerous_patterns:
        cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE | re.DOTALL)
    
    # Truncate if too long
    if len(cleaned) > max_length:
        cleaned = cleaned[:max_length] + "..."
    
    return cleaned.strip()

# ============================================================================
# DATE AND TIME UTILITIES
# ============================================================================

def get_market_timezone():
    """Get market timezone (Eastern Time)"""
    from zoneinfo import ZoneInfo
    return ZoneInfo("America/New_York")

def is_market_hours() -> bool:
    """Check if current time is during market hours"""
    try:
        now = datetime.now(get_market_timezone())
        
        # Market is closed on weekends
        if now.weekday() >= 5:  # Saturday = 5, Sunday = 6
            return False
        
        # Market hours: 9:30 AM - 4:00 PM ET
        market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
        
        return market_open <= now <= market_close
        
    except Exception:
        # Fallback: assume market is open during weekdays
        now = datetime.now()
        return now.weekday() < 5

def get_next_market_open() -> datetime:
    """Get the next market open time"""
    try:
        now = datetime.now(get_market_timezone())
        
        # If it's weekend, next open is Monday
        if now.weekday() >= 5:
            days_until_monday = 7 - now.weekday()
            next_open = now + timedelta(days=days_until_monday)
            return next_open.replace(hour=9, minute=30, second=0, microsecond=0)
        
        # If it's before market open today
        market_open_today = now.replace(hour=9, minute=30, second=0, microsecond=0)
        if now < market_open_today:
            return market_open_today
        
        # Otherwise, next open is tomorrow (or Monday if Friday)
        if now.weekday() == 4:  # Friday
            next_open = now + timedelta(days=3)  # Monday
        else:
            next_open = now + timedelta(days=1)  # Tomorrow
        
        return next_open.replace(hour=9, minute=30, second=0, microsecond=0)
        
    except Exception:
        # Fallback
        return datetime.now() + timedelta(hours=1)

def format_timestamp(dt: datetime, include_timezone: bool = True) -> str:
    """Format datetime for display"""
    if not dt:
        return "N/A"
    
    if include_timezone and dt.tzinfo:
        return dt.strftime("%Y-%m-%d %H:%M:%S %Z")
    else:
        return dt.strftime("%Y-%m-%d %H:%M:%S")

# ============================================================================
# FINANCIAL CALCULATIONS
# ============================================================================

def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """Calculate percentage change between two values"""
    if old_value == 0:
        return 0.0 if new_value == 0 else float('inf')
    
    return ((new_value - old_value) / old_value) * 100

def calculate_position_value(shares: int, price: float) -> float:
    """Calculate total position value"""
    return shares * price

def calculate_profit_loss(entry_price: float, current_price: float, shares: int) -> Tuple[float, float]:
    """Calculate profit/loss in dollars and percentage"""
    dollar_pnl = (current_price - entry_price) * shares
    percentage_pnl = calculate_percentage_change(entry_price, current_price)
    
    return dollar_pnl, percentage_pnl

def calculate_risk_reward_ratio(entry_price: float, target_price: float, stop_loss: float) -> float:
    """Calculate risk/reward ratio"""
    if entry_price == stop_loss:
        return 0.0
    
    reward = abs(target_price - entry_price)
    risk = abs(entry_price - stop_loss)
    
    return reward / risk if risk > 0 else 0.0

def calculate_position_size(account_value: float, risk_percent: float, 
                          entry_price: float, stop_loss: float) -> int:
    """Calculate optimal position size based on risk management"""
    if entry_price == stop_loss:
        return 0
    
    risk_amount = account_value * (risk_percent / 100)
    price_risk = abs(entry_price - stop_loss)
    
    if price_risk == 0:
        return 0
    
    position_value = risk_amount / (price_risk / entry_price)
    shares = int(position_value / entry_price)
    
    return max(0, shares)

# ============================================================================
# DATA STRUCTURES AND SERIALIZATION
# ============================================================================

def safe_json_serialize(obj: Any) -> str:
    """Safely serialize object to JSON"""
    def json_serializer(obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        elif hasattr(obj, '_asdict'):  # namedtuple
            return obj._asdict()
        else:
            return str(obj)
    
    try:
        return json.dumps(obj, default=json_serializer, indent=2)
    except Exception as e:
        logger.error(f"JSON serialization failed: {e}")
        return "{}"

def safe_json_deserialize(json_str: str, default: Any = None) -> Any:
    """Safely deserialize JSON string"""
    try:
        return json.loads(json_str)
    except Exception as e:
        logger.error(f"JSON deserialization failed: {e}")
        return default

def deep_merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """Deep merge two dictionaries"""
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dicts(result[key], value)
        else:
            result[key] = value
    
    return result

# ============================================================================
# CACHING AND MEMOIZATION
# ============================================================================

class TTLCache:
    """Time-to-live cache implementation"""
    
    def __init__(self, ttl_seconds: int = 300):
        self.ttl_seconds = ttl_seconds
        self.cache: Dict[str, Tuple[Any, float]] = {}
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        if key not in self.cache:
            return None
        
        value, timestamp = self.cache[key]
        
        # Check if expired
        if time.time() - timestamp > self.ttl_seconds:
            del self.cache[key]
            return None
        
        return value
    
    def set(self, key: str, value: Any):
        """Set value in cache"""
        self.cache[key] = (value, time.time())
    
    def clear(self):
        """Clear all cache entries"""
        self.cache.clear()
    
    def cleanup_expired(self):
        """Remove expired entries"""
        current_time = time.time()
        expired_keys = [
            key for key, (_, timestamp) in self.cache.items()
            if current_time - timestamp > self.ttl_seconds
        ]
        
        for key in expired_keys:
            del self.cache[key]

def memoize_with_ttl(ttl_seconds: int = 300):
    """Decorator for memoizing function results with TTL"""
    def decorator(func):
        cache = TTLCache(ttl_seconds)
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Create cache key
            key = hashlib.md5(
                f"{func.__name__}:{str(args)}:{str(sorted(kwargs.items()))}".encode()
            ).hexdigest()
            
            # Try to get from cache
            result = cache.get(key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(key, result)
            
            return result
        
        wrapper.cache = cache
        return wrapper
    
    return decorator

# ============================================================================
# RETRY AND ERROR HANDLING
# ============================================================================

def retry_with_backoff(max_retries: int = 3, backoff_factor: float = 1.0):
    """Decorator for retrying functions with exponential backoff"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        break
                    
                    wait_time = backoff_factor * (2 ** attempt)
                    logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}. Retrying in {wait_time}s...")
                    await asyncio.sleep(wait_time)
            
            logger.error(f"All {max_retries + 1} attempts failed for {func.__name__}")
            raise last_exception
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        break
                    
                    wait_time = backoff_factor * (2 ** attempt)
                    logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}. Retrying in {wait_time}s...")
                    time.sleep(wait_time)
            
            logger.error(f"All {max_retries + 1} attempts failed for {func.__name__}")
            raise last_exception
        
        return async_wrapper if inspect.iscoroutinefunction(func) else sync_wrapper
    
    return decorator

# ============================================================================
# FILE AND PATH UTILITIES
# ============================================================================

def ensure_directory(path: Union[str, Path]) -> Path:
    """Ensure directory exists, create if it doesn't"""
    path_obj = Path(path)
    path_obj.mkdir(parents=True, exist_ok=True)
    return path_obj

def safe_filename(filename: str) -> str:
    """Create a safe filename by removing invalid characters"""
    # Remove invalid characters
    safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove multiple underscores
    safe_name = re.sub(r'_+', '_', safe_name)
    
    # Trim and remove leading/trailing underscores
    safe_name = safe_name.strip('_')
    
    return safe_name or "unnamed"

def get_file_size_mb(file_path: Union[str, Path]) -> float:
    """Get file size in megabytes"""
    try:
        path_obj = Path(file_path)
        if path_obj.exists():
            size_bytes = path_obj.stat().st_size
            return round(size_bytes / (1024 * 1024), 2)
        return 0.0
    except Exception:
        return 0.0

# ============================================================================
# GLOBAL INSTANCES
# ============================================================================

# Global performance monitor
performance_monitor = PerformanceMonitor()

# Global cache instances
default_cache = TTLCache(300)  # 5 minutes
long_cache = TTLCache(3600)    # 1 hour

# ============================================================================
# CONVENIENCE FUNCTIONS
# ============================================================================

def log_function_call(func_name: str, args: tuple = None, kwargs: dict = None):
    """Log function call for debugging"""
    args_str = f"args={args}" if args else ""
    kwargs_str = f"kwargs={kwargs}" if kwargs else ""
    separator = ", " if args and kwargs else ""
    
    logger.debug(f"🔧 Calling {func_name}({args_str}{separator}{kwargs_str})")

def format_currency(amount: float, currency: str = "USD") -> str:
    """Format amount as currency"""
    if currency == "USD":
        return f"${amount:,.2f}"
    else:
        return f"{amount:,.2f} {currency}"

def format_percentage(value: float, decimal_places: int = 2) -> str:
    """Format value as percentage"""
    return f"{value:.{decimal_places}f}%"

def truncate_string(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """Truncate string to maximum length"""
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

# Export main utilities
__all__ = [
    # Decorators
    "timing_decorator", "memoize_with_ttl", "retry_with_backoff",
    
    # Classes
    "PerformanceMonitor", "TTLCache",
    
    # Validation functions
    "validate_symbol", "sanitize_price", "sanitize_volume", "clean_text",
    
    # Date/time functions
    "get_market_timezone", "is_market_hours", "get_next_market_open", "format_timestamp",
    
    # Financial calculations
    "calculate_percentage_change", "calculate_position_value", "calculate_profit_loss",
    "calculate_risk_reward_ratio", "calculate_position_size",
    
    # Data utilities
    "safe_json_serialize", "safe_json_deserialize", "deep_merge_dicts",
    
    # File utilities
    "ensure_directory", "safe_filename", "get_file_size_mb",
    
    # Formatting functions
    "format_currency", "format_percentage", "truncate_string",
    
    # Global instances
    "performance_monitor", "default_cache", "long_cache"
]
