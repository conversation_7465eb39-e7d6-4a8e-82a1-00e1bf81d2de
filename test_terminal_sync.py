#!/usr/bin/env python3
"""
Test Terminal Synchronization
Send commands to WebSocket and verify they appear in browser terminal
"""

import asyncio
import websockets
import json
from datetime import datetime

async def test_terminal_sync():
    """Test if commands sent via WebSocket appear in browser terminal"""
    uri = "ws://localhost:8002/ws/scanner"
    
    try:
        print("🔌 Connecting to A.T.L.A.S. WebSocket terminal...")
        
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to WebSocket terminal!")
            
            # Wait for welcome message
            try:
                welcome = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📨 Welcome: {welcome}")
            except asyncio.TimeoutError:
                print("⏰ No welcome message received")
            
            # Send test commands that should appear in browser terminal
            test_commands = ["help", "status", "health", "quote AAPL"]
            
            for cmd in test_commands:
                print(f"\n📤 Sending command: {cmd}")
                
                command_msg = {
                    "type": "command",
                    "command": cmd
                }
                
                await websocket.send(json.dumps(command_msg))
                
                # Wait for response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    print(f"💻 Response type: {response_data.get('type')}")
                    print(f"📄 Output preview: {response_data.get('output', '')[:100]}...")
                except asyncio.TimeoutError:
                    print("⏰ No response received")
                except Exception as e:
                    print(f"❌ Error processing response: {e}")
                
                # Wait a bit between commands
                await asyncio.sleep(2)
            
            print("\n🎯 Test completed! Check if these commands appeared in your browser terminal.")
            print("If they did, the terminal sync is working correctly.")
            print("If not, there may be a JavaScript issue in the browser.")
            
            # Keep connection alive for a bit to see periodic updates
            print("\n⏳ Waiting 35 seconds to see periodic updates...")
            try:
                for i in range(35):
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        data = json.loads(message)
                        print(f"📡 [{datetime.now().strftime('%H:%M:%S')}] {data.get('type', 'unknown')}: {data.get('message', data.get('output', ''))[:50]}...")
                    except asyncio.TimeoutError:
                        continue
                    except Exception as e:
                        print(f"❌ Error: {e}")
            except KeyboardInterrupt:
                print("\n⏹️ Test interrupted by user")
            
    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")

if __name__ == "__main__":
    print("🧪 Testing A.T.L.A.S. Terminal Synchronization")
    print("=" * 50)
    print("This test will send commands to the WebSocket terminal.")
    print("Check your browser terminal to see if the commands appear there.")
    print("=" * 50)
    
    asyncio.run(test_terminal_sync())
