"""
A.T.L.A.S. Pattern Detection Agent
Advanced Lee Method pattern recognition with 3-criteria validation
"""

import logging
from datetime import datetime
from typing import Dict, List, Any
from dataclasses import dataclass

from atlas_multi_agent_core import (
    AtlasBaseAgent, AgentRole, AgentCapabilities, MultiAgentTask
)
from atlas_lee_method import AtlasLeeMethodRealtimeScanner
from atlas_enhanced_realtime_scanner import EnhancedRealtimeScanner

logger = logging.getLogger(__name__)

@dataclass
class PatternDetectionResult:
    """Result of pattern detection analysis"""
    symbol: str
    pattern_detected: bool
    pattern_type: str
    strength: float
    confidence: float
    lee_method_signals: Dict[str, Any]
    technical_indicators: Dict[str, Any]
    timestamp: datetime

class AtlasPatternDetectionAgent(AtlasBaseAgent):
    """Pattern detection agent using Lee Method and technical analysis"""

    def __init__(self):
        super().__init__(AgentRole.PATTERN_DETECTOR)
        self.lee_method_scanner = None
        self.enhanced_scanner = None

    def _define_capabilities(self) -> AgentCapabilities:
        """Define pattern detection agent capabilities"""
        return AgentCapabilities(
            role=AgentRole.PATTERN_DETECTOR,
            supported_tasks=[
                "detect_lee_method_patterns",
                "analyze_technical_patterns",
                "momentum_analysis",
                "trend_confirmation",
                "pattern_strength_rating"
            ],
            max_concurrent_tasks=3,
            average_processing_time=25.0,
            success_rate=0.85,
            dependencies=[AgentRole.DATA_VALIDATOR]
        )

    async def _initialize_agent(self):
        """Initialize the pattern detection agent"""
        try:
            # Initialize Lee Method scanner
            self.lee_method_scanner = AtlasLeeMethodRealtimeScanner()

            # Initialize enhanced scanner
            self.enhanced_scanner = EnhancedRealtimeScanner()
            await self.enhanced_scanner.initialize()

            self.logger.info("Pattern detection agent initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize pattern detection agent: {e}")
            raise

    async def execute_task(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Execute a pattern detection task"""
        try:
            task_type = task.input_data.get("task_type", "detect_lee_method_patterns")
            symbol = task.input_data.get("symbol", "")

            if task_type == "detect_lee_method_patterns":
                return await self._detect_lee_method_patterns(symbol)
            elif task_type == "analyze_technical_patterns":
                return await self._analyze_technical_patterns(symbol)
            elif task_type == "momentum_analysis":
                return await self._analyze_momentum(symbol)
            elif task_type == "trend_confirmation":
                return await self._confirm_trend(symbol)
            elif task_type == "pattern_strength_rating":
                return await self._rate_pattern_strength(symbol)
            else:
                return {"error": f"Unknown task type: {task_type}"}

        except Exception as e:
            self.logger.error(f"Pattern detection task failed: {e}")
            self.failed_tasks += 1
            return {"error": str(e)}

    async def _detect_lee_method_patterns(self, symbol: str) -> Dict[str, Any]:
        """Detect Lee Method patterns for a symbol"""
        try:
            # Get Lee Method analysis
            lee_signals = await self.lee_method_scanner.scan_symbol(symbol)

            if not lee_signals:
                return {
                    "pattern_detected": False,
                    "symbol": symbol,
                    "error": "No Lee Method signals available",
                    "timestamp": datetime.now().isoformat()
                }

            # Analyze the 3-criteria validation
            trend_confirmed = lee_signals.get("trend_confirmation", False)
            volume_validated = lee_signals.get("volume_validation", False)
            technical_pattern = lee_signals.get("technical_pattern", False)

            # Calculate overall pattern strength
            criteria_met = sum([trend_confirmed, volume_validated, technical_pattern])
            pattern_strength = criteria_met / 3.0

            # Determine if pattern is detected (all 3 criteria must be met)
            pattern_detected = criteria_met == 3

            self.completed_tasks += 1
            return {
                "pattern_detected": pattern_detected,
                "pattern_type": "lee_method",
                "strength": pattern_strength,
                "confidence": lee_signals.get("confidence", 0.0),
                "criteria_analysis": {
                    "trend_confirmation": trend_confirmed,
                    "volume_validation": volume_validated,
                    "technical_pattern": technical_pattern,
                    "criteria_met": criteria_met
                },
                "lee_method_signals": lee_signals,
                "symbol": symbol,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Lee Method pattern detection failed for {symbol}: {e}")
            return {
                "pattern_detected": False,
                "error": str(e),
                "symbol": symbol,
                "timestamp": datetime.now().isoformat()
            }

    async def _analyze_technical_patterns(self, symbol: str) -> Dict[str, Any]:
        """Analyze technical patterns using enhanced scanner"""
        try:
            # Get technical analysis from enhanced scanner
            scan_result = await self.enhanced_scanner.scan_single_symbol(symbol)

            if not scan_result:
                return {
                    "patterns_found": [],
                    "symbol": symbol,
                    "error": "No technical analysis available"
                }

            # Extract pattern information
            patterns = []
            technical_data = scan_result.get("technical_analysis", {})

            # Check for common patterns
            if technical_data.get("breakout", False):
                patterns.append({
                    "type": "breakout",
                    "strength": technical_data.get("breakout_strength", 0.0),
                    "direction": "bullish" if technical_data.get("price_change", 0) > 0 else "bearish"
                })

            if technical_data.get("momentum", 0) > 0.7:
                patterns.append({
                    "type": "momentum",
                    "strength": technical_data.get("momentum", 0.0),
                    "direction": "bullish"
                })

            return {
                "patterns_found": patterns,
                "technical_indicators": technical_data,
                "symbol": symbol,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Technical pattern analysis failed for {symbol}: {e}")
            return {"patterns_found": [], "error": str(e), "symbol": symbol}

    async def _analyze_momentum(self, symbol: str) -> Dict[str, Any]:
        """Analyze momentum indicators"""
        try:
            # Get momentum data from enhanced scanner
            scan_result = await self.enhanced_scanner.scan_single_symbol(symbol)

            if not scan_result:
                return {"momentum_score": 0.0, "error": "No data available"}

            technical_data = scan_result.get("technical_analysis", {})
            momentum = technical_data.get("momentum", 0.0)
            volume_ratio = technical_data.get("volume_ratio", 1.0)

            # Calculate composite momentum score
            momentum_score = (momentum * 0.7) + (min(volume_ratio / 2.0, 1.0) * 0.3)

            return {
                "momentum_score": momentum_score,
                "momentum_raw": momentum,
                "volume_ratio": volume_ratio,
                "momentum_level": self._classify_momentum(momentum_score),
                "symbol": symbol,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {"momentum_score": 0.0, "error": str(e)}

    async def _confirm_trend(self, symbol: str) -> Dict[str, Any]:
        """Confirm trend direction and strength"""
        try:
            # Get trend data
            scan_result = await self.enhanced_scanner.scan_single_symbol(symbol)

            if not scan_result:
                return {"trend_confirmed": False, "error": "No data available"}

            market_data = scan_result.get("market_data", {})
            technical_data = scan_result.get("technical_analysis", {})

            # Analyze trend indicators
            price_change = market_data.get("change_percent", 0.0)
            volume_ratio = technical_data.get("volume_ratio", 1.0)
            momentum = technical_data.get("momentum", 0.0)

            # Trend confirmation logic
            trend_strength = abs(price_change) * 0.4 + momentum * 0.4 + min(volume_ratio / 2.0, 1.0) * 0.2
            trend_direction = "bullish" if price_change > 0 else "bearish"
            trend_confirmed = trend_strength > 0.6

            return {
                "trend_confirmed": trend_confirmed,
                "trend_direction": trend_direction,
                "trend_strength": trend_strength,
                "supporting_factors": {
                    "price_change": price_change,
                    "volume_confirmation": volume_ratio > 1.2,
                    "momentum_alignment": momentum > 0.5
                },
                "symbol": symbol,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {"trend_confirmed": False, "error": str(e)}

    async def _rate_pattern_strength(self, symbol: str) -> Dict[str, Any]:
        """Rate the overall pattern strength (1-5 stars)"""
        try:
            # Get comprehensive analysis
            lee_result = await self._detect_lee_method_patterns(symbol)
            momentum_result = await self._analyze_momentum(symbol)
            trend_result = await self._confirm_trend(symbol)

            # Calculate composite strength score
            lee_strength = lee_result.get("strength", 0.0) if lee_result.get("pattern_detected") else 0.0
            momentum_score = momentum_result.get("momentum_score", 0.0)
            trend_strength = trend_result.get("trend_strength", 0.0) if trend_result.get("trend_confirmed") else 0.0

            # Weighted composite score
            composite_score = (lee_strength * 0.5) + (momentum_score * 0.3) + (trend_strength * 0.2)

            # Convert to star rating (1-5)
            if composite_score >= 0.9:
                stars = 5
                rating = "Excellent"
            elif composite_score >= 0.75:
                stars = 4
                rating = "Strong"
            elif composite_score >= 0.6:
                stars = 3
                rating = "Good"
            elif composite_score >= 0.4:
                stars = 2
                rating = "Weak"
            else:
                stars = 1
                rating = "Very Weak"

            return {
                "pattern_strength_stars": stars,
                "pattern_strength_rating": rating,
                "composite_score": composite_score,
                "component_scores": {
                    "lee_method": lee_strength,
                    "momentum": momentum_score,
                    "trend": trend_strength
                },
                "symbol": symbol,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {"pattern_strength_stars": 1, "error": str(e)}

    def _classify_momentum(self, momentum_score: float) -> str:
        """Classify momentum level"""
        if momentum_score >= 0.8:
            return "Very Strong"
        elif momentum_score >= 0.6:
            return "Strong"
        elif momentum_score >= 0.4:
            return "Moderate"
        elif momentum_score >= 0.2:
            return "Weak"
        else:
            return "Very Weak"

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = ["AtlasPatternDetectionAgent", "PatternDetectionResult"]