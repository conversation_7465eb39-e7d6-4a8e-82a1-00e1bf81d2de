"""
A.T.L.A.S. Beginner Grok System
Simplified Grok integration for new users using the main Grok system
"""

import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class AtlasBeginnerGrokSystem:
    """
    Beginner-friendly Grok system interface that uses the main Grok integration
    """

    def __init__(self):
        self.is_available = True
        self.grok_integration = None
        logger.info("Beginner Grok system initialized")

    async def initialize(self):
        """Initialize the beginner Grok system"""
        try:
            from atlas_grok_integration import grok_integration
            self.grok_integration = grok_integration
            await self.grok_integration.initialize()
            self.is_available = self.grok_integration.is_available()
            logger.info("✅ Beginner Grok system ready")
        except Exception as e:
            logger.error(f"❌ Beginner Grok system initialization failed: {e}")
            self.is_available = False

    async def simple_analysis(self, symbol: str, question: str) -> Optional[str]:
        """
        Simple analysis for beginners using Grok AI
        """
        if not self.is_available or not self.grok_integration:
            return None

        try:
            # Create a beginner-friendly prompt
            beginner_prompt = f"""
            Please provide a simple, beginner-friendly analysis for {symbol}.
            Question: {question}

            Please explain in simple terms that a new trader can understand.
            Include:
            - What this stock does (company overview)
            - Current price situation
            - Simple recommendation (buy/hold/avoid)
            - Key things to watch

            Keep the language simple and educational.
            """

            response = await self.grok_integration.get_ai_response(beginner_prompt)
            return response.get('content') if response else None

        except Exception as e:
            logger.error(f"Simple analysis failed for {symbol}: {e}")
            return None

    def is_system_available(self) -> bool:
        """Check if beginner Grok system is available"""
        return self.is_available

# Global instance
beginner_grok_system = AtlasBeginnerGrokSystem()

__all__ = ["AtlasBeginnerGrokSystem", "beginner_grok_system"]
