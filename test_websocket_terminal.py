#!/usr/bin/env python3
"""
Test WebSocket Terminal Connection
"""

import asyncio
import websockets
import json
from datetime import datetime

async def test_websocket_terminal():
    """Test the WebSocket terminal functionality"""
    uri = "ws://localhost:8002/ws/scanner"
    
    try:
        print("🔌 Connecting to A.T.L.A.S. WebSocket terminal...")
        
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to WebSocket terminal!")
            
            # Listen for messages for 10 seconds
            print("📡 Listening for messages...")
            
            # Send a test command
            test_command = {
                "type": "command",
                "command": "help"
            }
            
            await websocket.send(json.dumps(test_command))
            print("📤 Sent 'help' command")
            
            # Listen for responses
            timeout = 10
            start_time = asyncio.get_event_loop().time()
            
            while (asyncio.get_event_loop().time() - start_time) < timeout:
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    data = json.loads(message)
                    
                    timestamp = data.get('timestamp', datetime.now().isoformat())
                    msg_type = data.get('type', 'unknown')
                    
                    if msg_type == 'system_log':
                        print(f"📊 [{timestamp}] SYSTEM: {data.get('message', '')}")
                    elif msg_type == 'command_response':
                        print(f"💻 [{timestamp}] RESPONSE: {data.get('output', '')}")
                    elif msg_type == 'scanner_alert':
                        print(f"🔍 [{timestamp}] ALERT: {data.get('symbol', '')} - {data.get('signal', '')}")
                    elif msg_type == 'error':
                        print(f"❌ [{timestamp}] ERROR: {data.get('message', '')}")
                    else:
                        print(f"📡 [{timestamp}] {msg_type.upper()}: {data}")
                        
                except asyncio.TimeoutError:
                    continue
                except Exception as e:
                    print(f"❌ Message processing error: {e}")
            
            # Test more commands
            commands = ["status", "health", "quote AAPL"]
            
            for cmd in commands:
                print(f"\n📤 Testing command: {cmd}")
                test_cmd = {
                    "type": "command", 
                    "command": cmd
                }
                await websocket.send(json.dumps(test_cmd))
                
                # Wait for response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(response)
                    print(f"💻 Response: {data.get('output', data)}")
                except asyncio.TimeoutError:
                    print("⏰ No response received")
                except Exception as e:
                    print(f"❌ Error: {e}")
            
            print("\n✅ WebSocket terminal test completed!")
            
    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_websocket_terminal())
