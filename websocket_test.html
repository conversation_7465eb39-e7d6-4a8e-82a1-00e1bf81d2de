<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
    <style>
        body { font-family: monospace; background: #1a1a1a; color: #00ff00; padding: 20px; }
        .log { margin: 5px 0; }
        .error { color: #ff4444; }
        .success { color: #4CAF50; }
        .info { color: #00aaff; }
        button { padding: 10px; margin: 5px; }
    </style>
</head>
<body>
    <h1>A.T.L.A.S. WebSocket Connection Test</h1>
    
    <button onclick="testConnection()">Test WebSocket Connection</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <div id="log"></div>

    <script>
        let socket = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log ${type}`;
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function testConnection() {
            if (socket) {
                socket.close();
                socket = null;
            }

            const wsUrl = `ws://${window.location.host}/ws/scanner`;
            log(`Attempting to connect to: ${wsUrl}`, 'info');

            try {
                socket = new WebSocket(wsUrl);

                socket.onopen = function(event) {
                    log('✅ WebSocket connection opened successfully!', 'success');
                    
                    // Send a test command
                    const testCommand = {
                        type: 'command',
                        command: 'help'
                    };
                    
                    socket.send(JSON.stringify(testCommand));
                    log('📤 Sent test command: help', 'info');
                };

                socket.onmessage = function(event) {
                    log(`📨 Received message: ${event.data}`, 'success');
                    
                    try {
                        const data = JSON.parse(event.data);
                        log(`📊 Parsed data: ${JSON.stringify(data, null, 2)}`, 'info');
                    } catch (e) {
                        log(`❌ Failed to parse JSON: ${e.message}`, 'error');
                    }
                };

                socket.onclose = function(event) {
                    log(`❌ WebSocket connection closed. Code: ${event.code}, Reason: ${event.reason || 'No reason provided'}`, 'error');
                    socket = null;
                };

                socket.onerror = function(event) {
                    log(`❌ WebSocket error occurred`, 'error');
                    console.error('WebSocket error:', event);
                };

            } catch (error) {
                log(`❌ Failed to create WebSocket: ${error.message}`, 'error');
            }
        }

        // Auto-test on page load
        window.onload = function() {
            log('WebSocket test page loaded', 'info');
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
