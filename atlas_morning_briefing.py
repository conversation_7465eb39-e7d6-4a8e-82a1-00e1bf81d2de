#!/usr/bin/env python3
"""
A.T.L.A.S. Morning Market Briefing System
Generates comprehensive market snapshots at market open and on-demand via chat
"""

import asyncio
import logging
import pytz
import aiohttp
from datetime import datetime, time as dt_time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import json

# Import existing A.T.L.A.S. components
from atlas_lee_method import LeeMethodScanner, LeeMethodSignal
from atlas_realtime_scanner import AtlasRealtimeScanner
from config import get_api_config

logger = logging.getLogger(__name__)

@dataclass
class TradeSetup:
    """Trade setup data structure"""
    symbol: str
    setup_type: str
    confidence: float
    entry_price: float
    target_price: float
    stop_loss: float
    risk_reward_ratio: float
    description: str
    stars: int  # 1-5 star rating
    
@dataclass
class SectorAnalysis:
    """Sector analysis data structure"""
    sector: str
    symbol: str  # ETF symbol
    performance: float
    status: str  # "Strong", "Weak", "Mixed"
    recommendation: str

@dataclass
class MarketBriefing:
    """Complete market briefing data structure"""
    timestamp: datetime
    market_status: str
    major_indexes: Dict[str, Dict[str, Any]]
    top_setups: List[TradeSetup]
    sector_analysis: List[SectorAnalysis]
    notable_news: List[str]
    beginner_tip: str
    trading_mode: str
    vix_level: float
    market_sentiment: str

class AtlasMorningBriefing:
    """A.T.L.A.S. Morning Briefing Generator"""
    
    def __init__(self):
        self.scanner = LeeMethodScanner()
        self.realtime_scanner = AtlasRealtimeScanner()
        self.last_briefing: Optional[MarketBriefing] = None
        self.briefing_generated_today = False

        # Market open time (9:30 AM ET)
        self.market_open_time = dt_time(9, 30)

        # Initialize real data APIs
        self.fmp_config = None
        self.alpaca_config = None
        self._initialize_apis()

        # Scanner integration status
        self.scanner_initialized = False
        self.realtime_scanner_status = "stopped"
        
        # Enhanced beginner tips pool with educational content
        self.beginner_tips = [
            "A clean setup means clear risk. If your stop and target are fuzzy—pass.",
            "Use 3:1 reward-to-risk setups. If you're risking $100, look for at least $300 potential profit.",
            "Volume confirms price action. High volume breakouts are more reliable than low volume ones.",
            "Never risk more than 2% of your account on a single trade.",
            "The best trades often feel uncomfortable at first. Trust your analysis over emotions.",
            "Markets trend 30% of the time and range 70%. Adjust your strategy accordingly.",
            "Support becomes resistance, and resistance becomes support once broken.",
            "When in doubt, stay out. There's always another opportunity tomorrow.",
            "Paper trade first! Practice with virtual money before risking real capital.",
            "Start small. Even experienced traders begin new strategies with small position sizes.",
            "Keep a trading journal. Track what works and what doesn't for continuous improvement.",
            "The market will be here tomorrow. Don't chase trades out of FOMO (fear of missing out).",
            "Learn to read price action. Charts tell the story of supply and demand.",
            "Diversification reduces risk. Don't put all your eggs in one basket.",
            "Cut losses quickly, let winners run. This is the golden rule of trading.",
            "Understand the difference between investing and trading. Know which one you're doing."
        ]

        # Risk management guidelines
        self.risk_management_tips = {
            "position_sizing": "Never risk more than 1-2% of your account on a single trade",
            "stop_losses": "Always set stop losses before entering a trade",
            "diversification": "Don't concentrate more than 10% in any single stock",
            "leverage": "Avoid leverage until you're consistently profitable",
            "emotions": "Take breaks when emotional - fear and greed are your enemies"
        }
        
        logger.info("✅ A.T.L.A.S. Morning Briefing System initialized")

    async def initialize_scanners(self):
        """Initialize and verify scanner integration"""
        try:
            logger.info("🔄 Initializing scanner integration...")

            # Initialize Lee Method scanner
            await self.scanner.initialize()

            # Initialize realtime scanner
            await self.realtime_scanner.initialize()

            # Verify scanner status
            scanner_status = await self.realtime_scanner.get_scanner_status()
            self.realtime_scanner_status = scanner_status.get('status', 'unknown')

            self.scanner_initialized = True
            logger.info("✅ Scanner integration initialized successfully")

            return True

        except Exception as e:
            logger.error(f"❌ Scanner integration failed: {e}")
            self.scanner_initialized = False
            return False

    async def get_scanner_integration_status(self) -> Dict[str, Any]:
        """Get comprehensive scanner integration status"""
        try:
            # Get Lee Method scanner status
            lee_status = self.scanner.get_market_status()

            # Get realtime scanner status
            realtime_status = await self.realtime_scanner.get_scanner_status()

            # Get active signals from both scanners
            lee_signals = len(self.scanner.signals)
            realtime_signals = len(await self.realtime_scanner.get_active_signals())

            return {
                "scanner_initialized": self.scanner_initialized,
                "lee_method_scanner": {
                    "status": "active" if lee_status['market_open'] else "standby",
                    "market_open": lee_status['market_open'],
                    "active_signals": lee_signals,
                    "confidence_threshold": lee_status['scanner_config']['confidence_threshold']
                },
                "realtime_scanner": {
                    "status": realtime_status.get('status', 'unknown'),
                    "is_running": realtime_status.get('is_running', False),
                    "active_signals": realtime_signals,
                    "market_hours": realtime_status.get('market_hours', False)
                },
                "integration_health": "healthy" if self.scanner_initialized else "degraded"
            }

        except Exception as e:
            logger.error(f"Error getting scanner integration status: {e}")
            return {
                "scanner_initialized": False,
                "integration_health": "error",
                "error": str(e)
            }

    def _initialize_apis(self):
        """Initialize real market data APIs"""
        try:
            self.fmp_config = get_api_config('fmp')
            self.alpaca_config = get_api_config('alpaca')
            logger.info("✅ Real market data APIs initialized")
        except Exception as e:
            logger.error(f"❌ API initialization failed: {e}")
            logger.warning("⚠️ Will attempt to use available APIs during data fetching")

    async def _fetch_real_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fetch real quote data from FMP API"""
        try:
            if not self.fmp_config or not self.fmp_config.get('api_key'):
                logger.warning(f"FMP API not available for {symbol}")
                return None

            url = f"https://financialmodelingprep.com/api/v3/quote/{symbol}"
            params = {'apikey': self.fmp_config['api_key']}

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data and len(data) > 0:
                            quote = data[0]
                            return {
                                'symbol': quote.get('symbol'),
                                'price': quote.get('price'),
                                'change': quote.get('change'),
                                'change_percent': quote.get('changesPercentage'),
                                'volume': quote.get('volume'),
                                'timestamp': datetime.now().isoformat(),
                                'source': 'FMP_REAL'
                            }
            return None

        except Exception as e:
            logger.error(f"Error fetching real quote for {symbol}: {e}")
            return None

    async def _fetch_sector_etf_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fetch real sector ETF data"""
        return await self._fetch_real_quote(symbol)

    async def _fetch_vix_data(self) -> Optional[float]:
        """Fetch real VIX data"""
        try:
            vix_data = await self._fetch_real_quote('VIX')
            if vix_data and vix_data.get('price'):
                return float(vix_data['price'])
            return None
        except Exception as e:
            logger.error(f"Error fetching VIX data: {e}")
            return None

    async def generate_morning_briefing(self) -> MarketBriefing:
        """Generate comprehensive morning market briefing"""
        logger.info("📊 Generating morning market briefing...")
        
        try:
            # Check if market is open
            market_status = self.scanner.get_market_status()
            
            # Generate major indexes analysis
            major_indexes = await self._analyze_major_indexes()
            
            # Get top trade setups using Lee Method
            top_setups = await self._get_top_trade_setups()
            
            # Analyze sectors
            sector_analysis = await self._analyze_sectors()
            
            # Get notable news (placeholder - would integrate with news API)
            notable_news = await self._get_notable_news()
            
            # Get beginner tip
            beginner_tip = self._get_daily_beginner_tip()
            
            # Determine trading mode
            trading_mode = self._determine_trading_mode(major_indexes, top_setups)
            
            # Calculate VIX and sentiment
            vix_level = await self._get_vix_level()
            market_sentiment = self._analyze_market_sentiment(vix_level, major_indexes)
            
            briefing = MarketBriefing(
                timestamp=datetime.now(),
                market_status=market_status['market_status'],
                major_indexes=major_indexes,
                top_setups=top_setups,
                sector_analysis=sector_analysis,
                notable_news=notable_news,
                beginner_tip=beginner_tip,
                trading_mode=trading_mode,
                vix_level=vix_level,
                market_sentiment=market_sentiment
            )
            
            self.last_briefing = briefing
            self.briefing_generated_today = True
            
            logger.info("✅ Morning briefing generated successfully")
            return briefing
            
        except Exception as e:
            logger.error(f"❌ Error generating morning briefing: {e}")
            raise
    
    async def _analyze_major_indexes(self) -> Dict[str, Dict[str, Any]]:
        """Analyze major market indexes using REAL market data"""
        logger.info("📊 Fetching real major index data...")

        # Define major indexes with their ETF symbols
        index_symbols = {
            "S&P 500": "SPY",
            "NASDAQ": "QQQ",
            "Dow Jones": "DIA"
        }

        indexes = {}

        for index_name, symbol in index_symbols.items():
            try:
                # Fetch real quote data
                quote_data = await self._fetch_real_quote(symbol)

                if quote_data:
                    # Generate description based on real data
                    change_percent = quote_data.get('change_percent', 0)
                    description = self._generate_index_description(index_name, change_percent)

                    indexes[index_name] = {
                        "symbol": symbol,
                        "price": quote_data.get('price', 0),
                        "change": change_percent,
                        "description": description,
                        "source": "REAL_DATA"
                    }
                    logger.info(f"✅ Real data for {index_name}: ${quote_data.get('price', 0):.2f} ({change_percent:+.2f}%)")
                else:
                    # Fallback - but log warning since this is live trading
                    logger.warning(f"⚠️ Could not fetch real data for {index_name} ({symbol}) - API may be down")
                    indexes[index_name] = {
                        "symbol": symbol,
                        "price": 0,
                        "change": 0,
                        "description": "Data unavailable - API error",
                        "source": "ERROR"
                    }

            except Exception as e:
                logger.error(f"❌ Error fetching {index_name} data: {e}")
                indexes[index_name] = {
                    "symbol": symbol,
                    "price": 0,
                    "change": 0,
                    "description": "Data fetch failed",
                    "source": "ERROR"
                }

        return indexes

    def _generate_index_description(self, index_name: str, change_percent: float) -> str:
        """Generate description based on real price movement"""
        abs_change = abs(change_percent)

        if abs_change >= 2.0:
            if change_percent > 0:
                return f"Strong rally - up {abs_change:.1f}%"
            else:
                return f"Sharp decline - down {abs_change:.1f}%"
        elif abs_change >= 1.0:
            if change_percent > 0:
                return f"Solid gains - advancing {abs_change:.1f}%"
            else:
                return f"Notable pullback - down {abs_change:.1f}%"
        elif abs_change >= 0.5:
            if change_percent > 0:
                return f"Modest gains - up {abs_change:.1f}%"
            else:
                return f"Minor pullback - down {abs_change:.1f}%"
        elif abs_change >= 0.1:
            if change_percent > 0:
                return f"Slight advance - up {abs_change:.1f}%"
            else:
                return f"Slight decline - down {abs_change:.1f}%"
        else:
            return "Trading flat - minimal movement"
    
    async def _get_top_trade_setups(self) -> List[TradeSetup]:
        """Get top trade setups using Lee Method scanner with enhanced analysis"""
        try:
            logger.info("🔍 Scanning for high-probability trade setups...")

            # Expanded symbol list for comprehensive scanning
            high_volume_symbols = [
                'TSLA', 'AAPL', 'NVDA', 'MSFT', 'GOOGL', 'META', 'AMZN', 'AMD', 'CRM', 'NFLX',
                'SPY', 'QQQ', 'IWM', 'UBER', 'PLTR', 'COIN', 'ROKU', 'SQ', 'PYPL', 'SHOP'
            ]

            # Scan for signals using real Lee Method
            signals = await self.scanner.scan_multiple_symbols(high_volume_symbols)

            # Enhanced setup analysis
            setups = []
            for signal in signals:
                if signal and signal.confidence >= 0.70:  # Slightly lower threshold for more opportunities
                    # Enhanced setup description
                    enhanced_description = await self._enhance_setup_description(signal)

                    setup = TradeSetup(
                        symbol=signal.symbol,
                        setup_type=signal.pattern_type,
                        confidence=signal.confidence,
                        entry_price=signal.entry_price,
                        target_price=signal.target_price,
                        stop_loss=signal.stop_loss,
                        risk_reward_ratio=signal.risk_reward_ratio,
                        description=enhanced_description,
                        stars=self._calculate_stars(signal.confidence)
                    )
                    setups.append(setup)
                    logger.info(f"✅ Trade setup found: {signal.symbol} ({signal.confidence:.0%} confidence)")

            # Advanced sorting: confidence + risk/reward ratio
            setups.sort(key=lambda x: (x.confidence * 0.7 + min(x.risk_reward_ratio / 3.0, 1.0) * 0.3), reverse=True)

            # Return top 5 setups for briefing
            top_setups = setups[:5]

            if top_setups:
                logger.info(f"📊 Found {len(top_setups)} high-quality trade setups")
            else:
                logger.info("📊 No high-confidence trade setups found (normal market condition)")

            return top_setups

        except Exception as e:
            logger.error(f"Error getting trade setups: {e}")
            # Return empty list - NO MOCK DATA for live trading account
            logger.warning("⚠️ No trade setups available - using real scanner data only")
            return []

    async def _enhance_setup_description(self, signal) -> str:
        """Enhance setup description with additional context"""
        try:
            # Get real quote data for additional context
            quote_data = await self._fetch_real_quote(signal.symbol)

            base_description = signal.description

            if quote_data:
                volume = quote_data.get('volume', 0)
                price = quote_data.get('price', 0)

                # Add volume context
                if volume > 1000000:  # High volume
                    base_description += " + High volume confirmation"

                # Add price level context
                if price > 200:
                    base_description += " + Premium stock"
                elif price < 50:
                    base_description += " + Value opportunity"

            return base_description

        except Exception as e:
            logger.error(f"Error enhancing setup description: {e}")
            return signal.description
    
    async def _analyze_sectors(self) -> List[SectorAnalysis]:
        """Analyze sector performance using REAL ETF data"""
        logger.info("📊 Fetching real sector ETF data...")

        # Define major sector ETFs
        sector_etfs = {
            "Energy": "XLE",
            "Consumer": "XLY",
            "Technology": "XLK",
            "Healthcare": "XLV",
            "Financials": "XLF",
            "Industrials": "XLI",
            "Materials": "XLB",
            "Utilities": "XLU"
        }

        sectors = []

        for sector_name, etf_symbol in sector_etfs.items():
            try:
                # Fetch real ETF data
                etf_data = await self._fetch_sector_etf_data(etf_symbol)

                if etf_data:
                    change_percent = etf_data.get('change_percent', 0)
                    status = self._determine_sector_status(change_percent)
                    recommendation = self._generate_sector_recommendation(sector_name, change_percent, status)

                    sectors.append(SectorAnalysis(
                        sector=sector_name,
                        symbol=etf_symbol,
                        performance=change_percent,
                        status=status,
                        recommendation=recommendation
                    ))

                    logger.info(f"✅ Real sector data for {sector_name}: {change_percent:+.1f}% ({status})")
                else:
                    logger.warning(f"⚠️ Could not fetch real data for {sector_name} ({etf_symbol})")
                    sectors.append(SectorAnalysis(
                        sector=sector_name,
                        symbol=etf_symbol,
                        performance=0.0,
                        status="Unknown",
                        recommendation="Data unavailable"
                    ))

            except Exception as e:
                logger.error(f"❌ Error fetching {sector_name} data: {e}")

        # Sort by performance (best to worst)
        sectors.sort(key=lambda x: x.performance, reverse=True)

        return sectors[:5]  # Return top 5 for briefing

    def _determine_sector_status(self, change_percent: float) -> str:
        """Determine sector status based on performance"""
        if change_percent >= 1.0:
            return "Strong"
        elif change_percent <= -1.0:
            return "Weak"
        else:
            return "Mixed"

    def _generate_sector_recommendation(self, sector: str, change_percent: float, status: str) -> str:
        """Generate sector recommendation based on real performance"""
        if status == "Strong":
            if sector == "Technology":
                return "AI/Tech momentum continues"
            elif sector == "Energy":
                return "Oil strength driving gains"
            elif sector == "Financials":
                return "Rate environment supportive"
            else:
                return f"Strong momentum in {sector.lower()}"
        elif status == "Weak":
            if sector == "Energy":
                return "Avoid today - oil weakness"
            elif sector == "Healthcare":
                return "Defensive rotation out"
            else:
                return f"Weakness in {sector.lower()}"
        else:
            return f"{sector} showing mixed signals"
    
    async def _get_notable_news(self) -> List[str]:
        """Get notable market news using real news APIs"""
        try:
            # Use the news insights engine for real news
            from atlas_news_insights_engine import AtlasNewsInsightsEngine

            news_engine = AtlasNewsInsightsEngine()
            await news_engine.initialize()

            # Get recent market news
            news_data = await news_engine.ingest_news_data(['SPY', 'QQQ', 'DIA'])

            if news_data and news_data.get('ingested_articles'):
                news_items = []
                for article in news_data['ingested_articles'][:3]:  # Top 3 news items
                    news_items.append(f"📰 {article.get('title', 'Market Update')}")
                return news_items
            else:
                return [
                    "📰 Market news monitoring active",
                    "💡 Check financial news sources for latest updates",
                    "⚠️ Always verify news from multiple sources before trading"
                ]

        except Exception as e:
            logger.error(f"Failed to get real news: {e}")
            return [
                "📰 Market news monitoring active",
                "💡 Check financial news sources for latest updates",
                "⚠️ Always verify news from multiple sources before trading"
            ]
    
    def _get_daily_beginner_tip(self) -> str:
        """Get daily beginner tip"""
        # Rotate through tips based on day of year
        day_index = datetime.now().timetuple().tm_yday % len(self.beginner_tips)
        return self.beginner_tips[day_index]
    
    def _determine_trading_mode(self, indexes: Dict, setups: List[TradeSetup]) -> str:
        """Determine recommended trading mode"""
        avg_change = sum(idx['change'] for idx in indexes.values()) / len(indexes)
        setup_count = len(setups)
        
        if avg_change > 0.5 and setup_count >= 3:
            return "Active Trading - Strong momentum"
        elif avg_change < -0.5:
            return "Defensive - Wait for clarity"
        elif setup_count >= 2:
            return "Selective - Quality setups available"
        else:
            return "Watchlist & Light Action - Look for confirmation"
    
    async def _get_vix_level(self) -> float:
        """Get REAL VIX level from market data"""
        try:
            real_vix = await self._fetch_vix_data()
            if real_vix is not None:
                logger.info(f"✅ Real VIX level: {real_vix:.2f}")
                return real_vix
            else:
                logger.warning("⚠️ Could not fetch real VIX data - API may be down")
                return 0.0  # Return 0 to indicate data unavailable
        except Exception as e:
            logger.error(f"❌ Error fetching VIX data: {e}")
            return 0.0
    
    
    async def _get_vix_fallback(self) -> float:
        """Get VIX fallback calculation based on market volatility"""
        try:
            # Calculate implied volatility from major indexes
            spy_data = await self._fetch_real_quote('SPY')
            if spy_data:
                # Simple volatility estimate based on price movement
                price_change = abs(spy_data.get('change_percent', 0))
                estimated_vix = max(12.0, min(30.0, price_change * 5 + 15))
                logger.info(f"📊 Using estimated VIX: {estimated_vix:.1f}")
                return estimated_vix
            return 16.0  # Market average
        except Exception as e:
            logger.error(f"VIX fallback calculation failed: {e}")
            return 16.0

    def _analyze_market_sentiment(self, vix: float, indexes: Dict) -> str:
        """Analyze overall market sentiment with enhanced analysis"""
        try:
            # Calculate market metrics
            avg_change = sum(idx['change'] for idx in indexes.values()) / len(indexes)
            positive_indexes = sum(1 for idx in indexes.values() if idx['change'] > 0)
            total_indexes = len(indexes)
            positive_ratio = positive_indexes / total_indexes if total_indexes > 0 else 0

            # Enhanced sentiment analysis
            if vix == 0:  # VIX data unavailable
                if avg_change > 0.5:
                    return "Bullish - Strong positive momentum"
                elif avg_change > 0:
                    return "Cautiously Bullish - Modest gains"
                elif avg_change > -0.5:
                    return "Neutral - Mixed signals"
                else:
                    return "Cautious - Negative momentum"

            # VIX-based analysis
            if vix < 12:
                return "Very Bullish - Extremely low fear, strong confidence"
            elif vix < 15 and avg_change > 0.3:
                return "Bullish - Low fear, positive momentum"
            elif vix < 15 and avg_change > -0.3:
                return "Neutral - Low fear but mixed direction"
            elif vix < 20 and positive_ratio >= 0.6:
                return "Cautiously Bullish - Moderate fear, mostly positive"
            elif vix < 25:
                return "Mixed - Moderate volatility and uncertainty"
            elif vix < 30:
                return "Bearish - High fear environment"
            else:
                return "Very Bearish - Extreme fear and volatility"

        except Exception as e:
            logger.error(f"Error analyzing market sentiment: {e}")
            return "Unknown - Analysis unavailable"

    def _analyze_sector_rotation(self, sectors: List[SectorAnalysis]) -> Dict[str, Any]:
        """Analyze sector rotation patterns"""
        try:
            if not sectors:
                return {"rotation_type": "Unknown", "leaders": [], "laggards": []}

            # Sort sectors by performance
            sorted_sectors = sorted(sectors, key=lambda x: x.performance, reverse=True)

            # Identify leaders and laggards
            leaders = [s for s in sorted_sectors[:3] if s.performance > 0.5]
            laggards = [s for s in sorted_sectors[-3:] if s.performance < -0.5]

            # Determine rotation type
            strong_sectors = [s for s in sectors if s.performance > 1.0]
            weak_sectors = [s for s in sectors if s.performance < -1.0]

            if len(strong_sectors) >= 2:
                rotation_type = "Risk-On Rotation"
            elif len(weak_sectors) >= 2:
                rotation_type = "Risk-Off Rotation"
            elif any(s.sector in ["Technology", "Consumer"] for s in leaders):
                rotation_type = "Growth Rotation"
            elif any(s.sector in ["Utilities", "Healthcare"] for s in leaders):
                rotation_type = "Defensive Rotation"
            else:
                rotation_type = "Neutral Rotation"

            return {
                "rotation_type": rotation_type,
                "leaders": [s.sector for s in leaders],
                "laggards": [s.sector for s in laggards],
                "breadth": len([s for s in sectors if s.performance > 0]) / len(sectors)
            }

        except Exception as e:
            logger.error(f"Error analyzing sector rotation: {e}")
            return {"rotation_type": "Unknown", "leaders": [], "laggards": []}
    
    def _calculate_stars(self, confidence: float) -> int:
        """Calculate star rating from confidence"""
        if confidence >= 0.85:
            return 5
        elif confidence >= 0.80:
            return 4
        elif confidence >= 0.75:
            return 3
        elif confidence >= 0.70:
            return 2
        else:
            return 1
    
    def format_briefing_for_chat(self, briefing: MarketBriefing) -> str:
        """Format briefing for chat interface display"""
        
        # Get current time in ET
        et_tz = pytz.timezone('US/Eastern')
        current_time = datetime.now(et_tz)
        
        output = f"""🌅 **MARKET SNAPSHOT – {current_time.strftime('%B %d, %Y')} (Morning Briefing)**
A.T.L.A.S v5.0 // Beginner Mode // Trade Ideas & Market Overview

📊 **Major Indexes**
"""
        
        # Add indexes
        for name, data in briefing.major_indexes.items():
            change_emoji = "📈" if data['change'] > 0 else "📉" if data['change'] < 0 else "➡️"
            output += f"{change_emoji} **{name}**: {data['price']:,.2f} ({data['change']:+.2f}%) – {data['description']}\n"
        
        output += f"\n**VIX**: {briefing.vix_level} – {briefing.market_sentiment}\n"
        
        # Add top setups
        if briefing.top_setups:
            output += "\n📈 **Top Trade Setups (Lee Method – Strong Signals)**\n\n"
            
            for i, setup in enumerate(briefing.top_setups, 1):
                stars = "⭐" * setup.stars
                output += f"**{i}️⃣ {setup.symbol}** – {stars}\n"
                output += f"• **Setup**: {setup.setup_type}\n"
                output += f"• **Entry**: ${setup.entry_price:.2f} • **Target**: ${setup.target_price:.2f} • **Stop**: ${setup.stop_loss:.2f}\n"
                output += f"• **{setup.description}**\n"
                output += f"• **Confidence**: {setup.confidence:.0%}\n\n"
        
        # Add sector analysis
        output += "📊 **Sector Focus**\n\n"
        for sector in briefing.sector_analysis:
            emoji = "🔋" if sector.sector == "Energy" else "🛍️" if sector.sector == "Consumer" else "🧠" if sector.sector == "Technology" else "🏥" if sector.sector == "Healthcare" else "🏦"
            status_emoji = "📈" if sector.status == "Strong" else "📉" if sector.status == "Weak" else "➡️"
            output += f"{emoji} **{sector.sector}**: {sector.status} ({sector.symbol} {sector.performance:+.1f}%) – {sector.recommendation}\n"
        
        # Add notable news
        if briefing.notable_news:
            output += "\n📡 **Notable News**\n\n"
            for news in briefing.notable_news:
                output += f"• {news}\n"
        
        # Add beginner tip with explanation
        output += f"\n📘 **Beginner Tip of the Day**\n🧠 \"{briefing.beginner_tip}\"\n"

        # Add risk management reminder
        risk_tip = self._get_daily_risk_tip()
        output += f"\n⚠️ **Risk Management Reminder**\n💡 {risk_tip}\n"

        # Add trading mode with explanation
        output += f"\n📈 **Today's Mode**: {briefing.trading_mode}\n"

        # Add beginner-friendly action items
        if "Active" in briefing.trading_mode:
            output += "✔️ Look for high-confidence setups with clear risk/reward\n"
            output += "✔️ Use proper position sizing (1-2% risk per trade)\n"
        elif "Defensive" in briefing.trading_mode:
            output += "✔️ Focus on cash preservation and defensive stocks\n"
            output += "✔️ Avoid high-risk trades until clarity returns\n"
        elif "Selective" in briefing.trading_mode:
            output += "✔️ Be patient and wait for the best setups only\n"
            output += "✔️ Quality over quantity in trade selection\n"
        else:
            output += "✔️ Look for confirmation before entry\n"
            output += "✔️ Consider paper trading to practice strategies\n"

        # Add educational footer
        output += f"\n💡 **Remember**: This is paper trading practice. Learn the patterns before using real money!\n"

        return output

    def _get_daily_risk_tip(self) -> str:
        """Get daily risk management tip"""
        tips = [
            "Never risk more than 2% of your account on any single trade",
            "Always set your stop loss before entering a position",
            "Don't put more than 10% of your portfolio in any one stock",
            "Take profits on the way up - you don't have to sell everything at once",
            "If you're emotional about a trade, step away and reassess",
            "Paper trade new strategies before using real money",
            "Keep a trading journal to track what works and what doesn't"
        ]

        # Rotate through tips based on day of year
        day_index = datetime.now().timetuple().tm_yday % len(tips)
        return tips[day_index]
    
    async def should_generate_briefing(self) -> bool:
        """Check if briefing should be generated"""
        market_status = self.scanner.get_market_status()
        
        # Generate if market is open and we haven't generated today
        if market_status['market_open'] and not self.briefing_generated_today:
            return True
        
        # Reset flag at market close
        if not market_status['market_open']:
            self.briefing_generated_today = False
        
        return False
    
    async def get_briefing_for_chat(self) -> str:
        """Get briefing formatted for chat interface"""
        try:
            # Check if we need to generate a new briefing
            if await self.should_generate_briefing() or self.last_briefing is None:
                briefing = await self.generate_morning_briefing()
            else:
                briefing = self.last_briefing
            
            return self.format_briefing_for_chat(briefing)
            
        except Exception as e:
            logger.error(f"Error getting briefing for chat: {e}")
            return "❌ Unable to generate market briefing at this time. Please try again."

# Global instance
morning_briefing = AtlasMorningBriefing()

# Export main components
__all__ = ['AtlasMorningBriefing', 'MarketBriefing', 'TradeSetup', 'morning_briefing']
