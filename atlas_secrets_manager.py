"""
A.T.L.A.S. Secrets Manager
Secure management of API keys, credentials, and sensitive configuration data
"""

import os
import json
import logging
import hashlib
import base64
from typing import Dict, Optional, Any, List
from dataclasses import dataclass
from pathlib import Path
import configparser

logger = logging.getLogger(__name__)

@dataclass
class SecretInfo:
    """Information about a managed secret"""
    name: str
    is_set: bool
    source: str  # 'env', 'file', 'default'
    masked_value: str
    last_updated: Optional[str] = None

class AtlasSecretsManager:
    """
    Secure secrets management system for A.T.L.A.S.
    Handles API keys, database credentials, and other sensitive data
    """
    
    def __init__(self, config_file: str = "config.ini"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self._secrets_cache = {}
        self._load_configuration()
    
    def _load_configuration(self):
        """Load configuration from file if it exists"""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file)
                logger.info(f"Loaded configuration from {self.config_file}")
            else:
                logger.info(f"Configuration file {self.config_file} not found, using environment variables only")
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
    
    def get_secret(self, secret_name: str, section: str = "DEFAULT", default: str = None) -> Optional[str]:
        """
        Get a secret value with fallback hierarchy:
        1. Environment variable
        2. Configuration file
        3. Default value
        """
        try:
            # Check cache first
            cache_key = f"{section}.{secret_name}"
            if cache_key in self._secrets_cache:
                return self._secrets_cache[cache_key]
            
            # 1. Try environment variable first (highest priority)
            env_var_name = f"ATLAS_{secret_name.upper()}"
            env_value = os.getenv(env_var_name)
            if env_value:
                self._secrets_cache[cache_key] = env_value
                logger.debug(f"Secret '{secret_name}' loaded from environment variable")
                return env_value
            
            # 2. Try configuration file
            if self.config.has_option(section, secret_name):
                config_value = self.config.get(section, secret_name)
                if config_value:
                    self._secrets_cache[cache_key] = config_value
                    logger.debug(f"Secret '{secret_name}' loaded from configuration file")
                    return config_value
            
            # 3. Use default value
            if default is not None:
                self._secrets_cache[cache_key] = default
                logger.debug(f"Secret '{secret_name}' using default value")
                return default
            
            logger.warning(f"Secret '{secret_name}' not found in any source")
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving secret '{secret_name}': {e}")
            return default
    
    def set_secret(self, secret_name: str, value: str, section: str = "DEFAULT", persist: bool = False):
        """
        Set a secret value
        If persist=True, saves to configuration file
        """
        try:
            cache_key = f"{section}.{secret_name}"
            self._secrets_cache[cache_key] = value
            
            if persist:
                if not self.config.has_section(section) and section != "DEFAULT":
                    self.config.add_section(section)
                
                self.config.set(section, secret_name, value)
                
                # Save to file
                with open(self.config_file, 'w') as f:
                    self.config.write(f)
                
                logger.info(f"Secret '{secret_name}' saved to configuration file")
            else:
                logger.debug(f"Secret '{secret_name}' set in memory only")
                
        except Exception as e:
            logger.error(f"Error setting secret '{secret_name}': {e}")
    
    def get_api_config(self, api_name: str) -> Dict[str, Any]:
        """Get complete API configuration"""
        try:
            config = {}
            
            # Common API configuration keys
            common_keys = ['api_key', 'base_url', 'timeout', 'rate_limit', 'enabled']
            
            # API-specific keys
            api_specific_keys = {
                'grok': ['model', 'temperature', 'max_tokens'],
                'openai': ['model', 'temperature', 'max_tokens', 'organization'],
                'fmp': ['version'],
                'alpha_vantage': ['function'],
                'polygon': ['version'],
                'iex': ['version'],
                'twitter': ['bearer_token'],
                'reddit': ['client_id', 'client_secret', 'user_agent']
            }
            
            # Get all relevant keys
            all_keys = common_keys + api_specific_keys.get(api_name, [])
            
            for key in all_keys:
                secret_name = f"{api_name}_{key}"
                value = self.get_secret(secret_name, section=api_name.upper())
                if value is not None:
                    config[key] = value
            
            # Set availability flag
            config['available'] = config.get('api_key') is not None or config.get('enabled', 'false').lower() == 'true'
            
            return config
            
        except Exception as e:
            logger.error(f"Error getting API config for '{api_name}': {e}")
            return {'available': False}
    
    def get_database_config(self, db_name: str = "default") -> Dict[str, Any]:
        """Get database configuration"""
        try:
            config = {}
            
            # Database configuration keys
            db_keys = ['host', 'port', 'database', 'username', 'password', 'ssl_mode']
            
            for key in db_keys:
                secret_name = f"db_{key}" if db_name == "default" else f"{db_name}_db_{key}"
                value = self.get_secret(secret_name, section="DATABASE")
                if value is not None:
                    config[key] = value
            
            return config
            
        except Exception as e:
            logger.error(f"Error getting database config for '{db_name}': {e}")
            return {}
    
    def list_secrets(self, section: str = None) -> List[SecretInfo]:
        """List all managed secrets with their status"""
        try:
            secrets = []
            
            # Get secrets from environment variables
            for key, value in os.environ.items():
                if key.startswith('ATLAS_'):
                    secret_name = key[6:].lower()  # Remove 'ATLAS_' prefix
                    masked_value = self._mask_value(value)
                    secrets.append(SecretInfo(
                        name=secret_name,
                        is_set=True,
                        source='env',
                        masked_value=masked_value
                    ))
            
            # Get secrets from configuration file
            sections_to_check = [section] if section else self.config.sections() + ['DEFAULT']
            
            for sect in sections_to_check:
                if sect in self.config:
                    for key in self.config[sect]:
                        value = self.config[sect][key]
                        masked_value = self._mask_value(value)
                        secrets.append(SecretInfo(
                            name=f"{sect.lower()}.{key}" if sect != 'DEFAULT' else key,
                            is_set=bool(value),
                            source='file',
                            masked_value=masked_value
                        ))
            
            return secrets
            
        except Exception as e:
            logger.error(f"Error listing secrets: {e}")
            return []
    
    def _mask_value(self, value: str) -> str:
        """Mask sensitive values for display"""
        if not value:
            return ""
        
        if len(value) <= 8:
            return "*" * len(value)
        
        # Show first 2 and last 2 characters
        return value[:2] + "*" * (len(value) - 4) + value[-2:]
    
    def validate_secrets(self) -> Dict[str, bool]:
        """Validate that required secrets are available"""
        required_secrets = {
            'grok_api_key': False,
            'openai_api_key': False,
            'fmp_api_key': False,
            'alpha_vantage_api_key': False,
            'polygon_api_key': False
        }
        
        for secret_name in required_secrets.keys():
            value = self.get_secret(secret_name)
            required_secrets[secret_name] = value is not None and len(value) > 0
        
        return required_secrets
    
    def get_encryption_key(self) -> str:
        """Get or generate encryption key for sensitive data"""
        key = self.get_secret('encryption_key')
        if not key:
            # Generate a new key
            key = base64.b64encode(os.urandom(32)).decode('utf-8')
            self.set_secret('encryption_key', key, persist=True)
            logger.info("Generated new encryption key")
        
        return key
    
    def clear_cache(self):
        """Clear the secrets cache"""
        self._secrets_cache.clear()
        logger.info("Secrets cache cleared")
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check on secrets management"""
        try:
            validation_results = self.validate_secrets()
            secrets_list = self.list_secrets()
            
            total_secrets = len(secrets_list)
            valid_secrets = sum(1 for s in secrets_list if s.is_set)
            
            return {
                'status': 'healthy' if valid_secrets > 0 else 'degraded',
                'total_secrets': total_secrets,
                'valid_secrets': valid_secrets,
                'validation_results': validation_results,
                'config_file_exists': os.path.exists(self.config_file),
                'cache_size': len(self._secrets_cache)
            }
            
        except Exception as e:
            logger.error(f"Secrets manager health check failed: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }

# Global secrets manager instance
secrets_manager = AtlasSecretsManager()

# Convenience functions
def get_api_key(api_name: str) -> Optional[str]:
    """Get API key for specified service"""
    return secrets_manager.get_secret(f"{api_name}_api_key")

def get_api_config(api_name: str) -> Dict[str, Any]:
    """Get complete API configuration"""
    return secrets_manager.get_api_config(api_name)

def is_api_available(api_name: str) -> bool:
    """Check if API is available (has valid configuration)"""
    config = get_api_config(api_name)
    return config.get('available', False)

# Export main classes and functions
__all__ = [
    "AtlasSecretsManager",
    "SecretInfo",
    "secrets_manager",
    "get_api_key",
    "get_api_config", 
    "is_api_available"
]
