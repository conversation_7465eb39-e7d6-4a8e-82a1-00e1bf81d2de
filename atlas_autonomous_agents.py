"""
A.T.L.A.S. Autonomous Agents Manager
Advanced autonomous trading agents using the multi-agent orchestrator
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

class AgentType(Enum):
    """Types of autonomous agents"""
    SCALPING_AGENT = "scalping"
    SWING_AGENT = "swing"
    MOMENTUM_AGENT = "momentum"
    ARBITRAGE_AGENT = "arbitrage"

class AgentStatus(Enum):
    """Status of autonomous agents"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    PAUSED = "paused"

@dataclass
class AutonomousAgent:
    """Represents an autonomous trading agent"""
    agent_id: str
    agent_type: AgentType
    is_active: bool
    performance_score: float
    trades_executed: int
    created_at: datetime

class AtlasAutonomousAgentManager:
    """
    Manager for autonomous trading agents using the multi-agent orchestrator
    """

    def __init__(self):
        self.agents: Dict[str, AutonomousAgent] = {}
        self.is_available = True
        self.orchestrator = None
        logger.info("Autonomous agent manager initialized")

    async def initialize(self, orchestrator=None):
        """Initialize the autonomous agent manager"""
        try:
            if orchestrator:
                self.orchestrator = orchestrator
                self.is_available = True
                logger.info("✅ Autonomous agent manager ready")
            else:
                logger.warning("No orchestrator provided to autonomous agent manager")
                self.is_available = False
        except Exception as e:
            logger.error(f"❌ Autonomous agent manager initialization failed: {e}")
            self.is_available = False

    async def create_agent(self, agent_type: AgentType) -> Optional[str]:
        """
        Create a new autonomous agent using the orchestrator
        """
        if not self.is_available or not self.orchestrator:
            logger.warning("Cannot create agent - orchestrator not available")
            return None

        try:
            # Generate unique agent ID
            import uuid
            agent_id = f"{agent_type.value}_{uuid.uuid4().hex[:8]}"

            # Create agent instance
            agent = AutonomousAgent(
                agent_id=agent_id,
                agent_type=agent_type,
                status=AgentStatus.ACTIVE,
                created_at=datetime.now()
            )

            # Register with orchestrator
            self.agents[agent_id] = agent
            logger.info(f"✅ Created autonomous agent: {agent_id} ({agent_type.value})")
            return agent_id

        except Exception as e:
            logger.error(f"Failed to create agent {agent_type.value}: {e}")
            return None

    def get_active_agents(self) -> List[AutonomousAgent]:
        """Get list of active agents"""
        return [agent for agent in self.agents.values() if agent.status == AgentStatus.ACTIVE]

    def is_manager_available(self) -> bool:
        """Check if autonomous agent manager is available"""
        return self.is_available

# Global instance
autonomous_agent_manager = AtlasAutonomousAgentManager()

__all__ = ["AtlasAutonomousAgentManager", "AutonomousAgent", "AgentType", "autonomous_agent_manager"]
