"""
A.T.L.A.S. System Health Monitor
Comprehensive system health monitoring and diagnostics
"""

import asyncio
import logging
import psutil
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

# Core imports
from atlas_database import database_manager
from atlas_utils import performance_monitor, format_timestamp
from config import settings

logger = logging.getLogger(__name__)

class HealthStatus(Enum):
    """System health status levels"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

@dataclass
class HealthMetric:
    """Individual health metric"""
    name: str
    value: float
    unit: str
    status: HealthStatus
    threshold_warning: float
    threshold_critical: float
    description: str
    timestamp: datetime

@dataclass
class SystemHealthReport:
    """Complete system health report"""
    overall_status: HealthStatus
    overall_score: float
    timestamp: datetime
    metrics: List[HealthMetric]
    recommendations: List[str]
    alerts: List[str]

class AtlasSystemHealthMonitor:
    """
    Comprehensive system health monitoring for A.T.L.A.S.
    Monitors CPU, memory, disk, database, API connections, and trading performance
    """
    
    def __init__(self):
        self.monitoring_active = False
        self.health_history: List[SystemHealthReport] = []
        self.max_history = 100
        
        # Health thresholds
        self.thresholds = {
            "cpu_usage": {"warning": 70.0, "critical": 90.0},
            "memory_usage": {"warning": 80.0, "critical": 95.0},
            "disk_usage": {"warning": 85.0, "critical": 95.0},
            "database_response_time": {"warning": 1.0, "critical": 5.0},
            "api_response_time": {"warning": 2.0, "critical": 10.0},
            "error_rate": {"warning": 5.0, "critical": 15.0},
            "active_connections": {"warning": 50, "critical": 100}
        }
        
        logger.info("System health monitor initialized")
    
    async def start_monitoring(self, interval_seconds: int = 60):
        """Start continuous health monitoring"""
        try:
            self.monitoring_active = True
            logger.info(f"Starting health monitoring with {interval_seconds}s interval")
            
            while self.monitoring_active:
                try:
                    # Generate health report
                    report = await self.generate_health_report()
                    
                    # Store in history
                    self._store_health_report(report)
                    
                    # Check for alerts
                    await self._process_health_alerts(report)
                    
                    # Log status
                    logger.info(f"Health check: {report.overall_status.value} (score: {report.overall_score:.1f})")
                    
                    # Wait for next check
                    await asyncio.sleep(interval_seconds)
                    
                except Exception as e:
                    logger.error(f"Health monitoring error: {e}")
                    await asyncio.sleep(interval_seconds)
            
        except Exception as e:
            logger.error(f"Health monitoring failed: {e}")
        finally:
            self.monitoring_active = False
    
    def stop_monitoring(self):
        """Stop health monitoring"""
        self.monitoring_active = False
        logger.info("Health monitoring stopped")
    
    async def generate_health_report(self) -> SystemHealthReport:
        """Generate comprehensive health report"""
        try:
            metrics = []
            
            # System metrics
            metrics.extend(await self._get_system_metrics())
            
            # Database metrics
            metrics.extend(await self._get_database_metrics())
            
            # API metrics
            metrics.extend(await self._get_api_metrics())
            
            # Trading metrics
            metrics.extend(await self._get_trading_metrics())
            
            # Calculate overall status and score
            overall_status, overall_score = self._calculate_overall_health(metrics)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(metrics)
            
            # Generate alerts
            alerts = self._generate_alerts(metrics)
            
            return SystemHealthReport(
                overall_status=overall_status,
                overall_score=overall_score,
                timestamp=datetime.now(),
                metrics=metrics,
                recommendations=recommendations,
                alerts=alerts
            )
            
        except Exception as e:
            logger.error(f"Failed to generate health report: {e}")
            return SystemHealthReport(
                overall_status=HealthStatus.UNKNOWN,
                overall_score=0.0,
                timestamp=datetime.now(),
                metrics=[],
                recommendations=["Unable to generate health report"],
                alerts=["Health monitoring system error"]
            )
    
    async def _get_system_metrics(self) -> List[HealthMetric]:
        """Get system resource metrics"""
        metrics = []
        
        try:
            # CPU Usage
            cpu_percent = psutil.cpu_percent(interval=1)
            metrics.append(HealthMetric(
                name="cpu_usage",
                value=cpu_percent,
                unit="%",
                status=self._get_status("cpu_usage", cpu_percent),
                threshold_warning=self.thresholds["cpu_usage"]["warning"],
                threshold_critical=self.thresholds["cpu_usage"]["critical"],
                description="CPU utilization percentage",
                timestamp=datetime.now()
            ))
            
            # Memory Usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            metrics.append(HealthMetric(
                name="memory_usage",
                value=memory_percent,
                unit="%",
                status=self._get_status("memory_usage", memory_percent),
                threshold_warning=self.thresholds["memory_usage"]["warning"],
                threshold_critical=self.thresholds["memory_usage"]["critical"],
                description="Memory utilization percentage",
                timestamp=datetime.now()
            ))
            
            # Disk Usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            metrics.append(HealthMetric(
                name="disk_usage",
                value=disk_percent,
                unit="%",
                status=self._get_status("disk_usage", disk_percent),
                threshold_warning=self.thresholds["disk_usage"]["warning"],
                threshold_critical=self.thresholds["disk_usage"]["critical"],
                description="Disk space utilization percentage",
                timestamp=datetime.now()
            ))
            
            # Network connections
            connections = len(psutil.net_connections())
            metrics.append(HealthMetric(
                name="active_connections",
                value=connections,
                unit="count",
                status=self._get_status("active_connections", connections),
                threshold_warning=self.thresholds["active_connections"]["warning"],
                threshold_critical=self.thresholds["active_connections"]["critical"],
                description="Number of active network connections",
                timestamp=datetime.now()
            ))
            
        except Exception as e:
            logger.error(f"Failed to get system metrics: {e}")
        
        return metrics
    
    async def _get_database_metrics(self) -> List[HealthMetric]:
        """Get database health metrics"""
        metrics = []
        
        try:
            # Database connection test
            start_time = time.time()
            db_status = await database_manager.get_database_status()
            response_time = time.time() - start_time
            
            # Database response time
            metrics.append(HealthMetric(
                name="database_response_time",
                value=response_time,
                unit="seconds",
                status=self._get_status("database_response_time", response_time),
                threshold_warning=self.thresholds["database_response_time"]["warning"],
                threshold_critical=self.thresholds["database_response_time"]["critical"],
                description="Database query response time",
                timestamp=datetime.now()
            ))
            
            # Database connections
            connected_dbs = db_status.get("connected_databases", 0)
            total_dbs = db_status.get("total_databases", 6)
            connection_ratio = (connected_dbs / total_dbs) * 100 if total_dbs > 0 else 0
            
            metrics.append(HealthMetric(
                name="database_connections",
                value=connection_ratio,
                unit="%",
                status=HealthStatus.HEALTHY if connection_ratio == 100 else HealthStatus.CRITICAL,
                threshold_warning=90.0,
                threshold_critical=50.0,
                description="Percentage of database connections active",
                timestamp=datetime.now()
            ))
            
            # Database size
            total_size = db_status.get("total_size_mb", 0)
            metrics.append(HealthMetric(
                name="database_size",
                value=total_size,
                unit="MB",
                status=HealthStatus.HEALTHY if total_size < 1000 else HealthStatus.WARNING,
                threshold_warning=1000.0,
                threshold_critical=5000.0,
                description="Total database size in megabytes",
                timestamp=datetime.now()
            ))
            
        except Exception as e:
            logger.error(f"Failed to get database metrics: {e}")
            # Add error metric
            metrics.append(HealthMetric(
                name="database_error",
                value=1.0,
                unit="error",
                status=HealthStatus.CRITICAL,
                threshold_warning=0.0,
                threshold_critical=1.0,
                description="Database connection error",
                timestamp=datetime.now()
            ))
        
        return metrics
    
    async def _get_api_metrics(self) -> List[HealthMetric]:
        """Get API health metrics"""
        metrics = []
        
        try:
            # API response time (simulated)
            api_response_time = 0.5  # Would measure actual API calls
            metrics.append(HealthMetric(
                name="api_response_time",
                value=api_response_time,
                unit="seconds",
                status=self._get_status("api_response_time", api_response_time),
                threshold_warning=self.thresholds["api_response_time"]["warning"],
                threshold_critical=self.thresholds["api_response_time"]["critical"],
                description="Average API response time",
                timestamp=datetime.now()
            ))
            
            # API availability (check key APIs)
            api_availability = 100.0  # Would check actual API status
            metrics.append(HealthMetric(
                name="api_availability",
                value=api_availability,
                unit="%",
                status=HealthStatus.HEALTHY if api_availability >= 95 else HealthStatus.WARNING,
                threshold_warning=95.0,
                threshold_critical=80.0,
                description="Percentage of APIs available",
                timestamp=datetime.now()
            ))
            
        except Exception as e:
            logger.error(f"Failed to get API metrics: {e}")
        
        return metrics
    
    async def _get_trading_metrics(self) -> List[HealthMetric]:
        """Get trading system metrics"""
        metrics = []
        
        try:
            # Error rate (simulated)
            error_rate = 2.0  # Would calculate from actual error logs
            metrics.append(HealthMetric(
                name="error_rate",
                value=error_rate,
                unit="%",
                status=self._get_status("error_rate", error_rate),
                threshold_warning=self.thresholds["error_rate"]["warning"],
                threshold_critical=self.thresholds["error_rate"]["critical"],
                description="System error rate percentage",
                timestamp=datetime.now()
            ))
            
            # Trading system availability
            trading_availability = 100.0  # Would check trading engine status
            metrics.append(HealthMetric(
                name="trading_availability",
                value=trading_availability,
                unit="%",
                status=HealthStatus.HEALTHY if trading_availability >= 99 else HealthStatus.WARNING,
                threshold_warning=99.0,
                threshold_critical=95.0,
                description="Trading system availability percentage",
                timestamp=datetime.now()
            ))
            
        except Exception as e:
            logger.error(f"Failed to get trading metrics: {e}")
        
        return metrics
    
    def _get_status(self, metric_name: str, value: float) -> HealthStatus:
        """Determine health status based on thresholds"""
        if metric_name not in self.thresholds:
            return HealthStatus.UNKNOWN
        
        thresholds = self.thresholds[metric_name]
        
        if value >= thresholds["critical"]:
            return HealthStatus.CRITICAL
        elif value >= thresholds["warning"]:
            return HealthStatus.WARNING
        else:
            return HealthStatus.HEALTHY
    
    def _calculate_overall_health(self, metrics: List[HealthMetric]) -> tuple[HealthStatus, float]:
        """Calculate overall health status and score"""
        if not metrics:
            return HealthStatus.UNKNOWN, 0.0
        
        # Count status levels
        status_counts = {
            HealthStatus.HEALTHY: 0,
            HealthStatus.WARNING: 0,
            HealthStatus.CRITICAL: 0,
            HealthStatus.UNKNOWN: 0
        }
        
        for metric in metrics:
            status_counts[metric.status] += 1
        
        total_metrics = len(metrics)
        
        # Calculate score (0-100)
        score = (
            (status_counts[HealthStatus.HEALTHY] * 100) +
            (status_counts[HealthStatus.WARNING] * 60) +
            (status_counts[HealthStatus.CRITICAL] * 20) +
            (status_counts[HealthStatus.UNKNOWN] * 40)
        ) / total_metrics
        
        # Determine overall status
        if status_counts[HealthStatus.CRITICAL] > 0:
            overall_status = HealthStatus.CRITICAL
        elif status_counts[HealthStatus.WARNING] > 0:
            overall_status = HealthStatus.WARNING
        elif status_counts[HealthStatus.UNKNOWN] > total_metrics * 0.3:
            overall_status = HealthStatus.UNKNOWN
        else:
            overall_status = HealthStatus.HEALTHY
        
        return overall_status, score
    
    def _generate_recommendations(self, metrics: List[HealthMetric]) -> List[str]:
        """Generate health recommendations"""
        recommendations = []
        
        for metric in metrics:
            if metric.status == HealthStatus.CRITICAL:
                if metric.name == "cpu_usage":
                    recommendations.append("High CPU usage detected - consider optimizing processes")
                elif metric.name == "memory_usage":
                    recommendations.append("High memory usage detected - check for memory leaks")
                elif metric.name == "disk_usage":
                    recommendations.append("Low disk space - clean up old files or expand storage")
                elif metric.name == "database_response_time":
                    recommendations.append("Slow database response - check database performance")
                elif metric.name == "error_rate":
                    recommendations.append("High error rate - investigate system logs")
            
            elif metric.status == HealthStatus.WARNING:
                if metric.name == "cpu_usage":
                    recommendations.append("Monitor CPU usage - approaching high levels")
                elif metric.name == "memory_usage":
                    recommendations.append("Monitor memory usage - consider optimization")
                elif metric.name == "api_response_time":
                    recommendations.append("API response times elevated - check network connectivity")
        
        return recommendations
    
    def _generate_alerts(self, metrics: List[HealthMetric]) -> List[str]:
        """Generate health alerts"""
        alerts = []
        
        critical_metrics = [m for m in metrics if m.status == HealthStatus.CRITICAL]
        
        for metric in critical_metrics:
            alerts.append(f"CRITICAL: {metric.description} is {metric.value}{metric.unit}")
        
        return alerts
    
    def _store_health_report(self, report: SystemHealthReport):
        """Store health report in history"""
        self.health_history.append(report)
        
        # Maintain history limit
        if len(self.health_history) > self.max_history:
            self.health_history.pop(0)
    
    async def _process_health_alerts(self, report: SystemHealthReport):
        """Process health alerts"""
        if report.overall_status == HealthStatus.CRITICAL:
            logger.critical(f"SYSTEM HEALTH CRITICAL: Score {report.overall_score:.1f}")
            for alert in report.alerts:
                logger.critical(f"ALERT: {alert}")
        
        elif report.overall_status == HealthStatus.WARNING:
            logger.warning(f"SYSTEM HEALTH WARNING: Score {report.overall_score:.1f}")
            for alert in report.alerts:
                logger.warning(f"WARNING: {alert}")
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get health summary"""
        if not self.health_history:
            return {"status": "no_data", "message": "No health data available"}
        
        latest_report = self.health_history[-1]
        
        return {
            "status": latest_report.overall_status.value,
            "score": latest_report.overall_score,
            "timestamp": format_timestamp(latest_report.timestamp),
            "metrics_count": len(latest_report.metrics),
            "alerts_count": len(latest_report.alerts),
            "recommendations_count": len(latest_report.recommendations),
            "monitoring_active": self.monitoring_active
        }
    
    def get_detailed_report(self) -> Optional[Dict[str, Any]]:
        """Get detailed health report"""
        if not self.health_history:
            return None
        
        latest_report = self.health_history[-1]
        
        return {
            "overall_status": latest_report.overall_status.value,
            "overall_score": latest_report.overall_score,
            "timestamp": format_timestamp(latest_report.timestamp),
            "metrics": [asdict(metric) for metric in latest_report.metrics],
            "recommendations": latest_report.recommendations,
            "alerts": latest_report.alerts
        }

# Global health monitor instance
system_health_monitor = AtlasSystemHealthMonitor()

# Convenience functions
async def get_health_status() -> Dict[str, Any]:
    """Get current health status"""
    return system_health_monitor.get_health_summary()

async def generate_health_report() -> SystemHealthReport:
    """Generate health report"""
    return await system_health_monitor.generate_health_report()

async def start_health_monitoring(interval_seconds: int = 60):
    """Start health monitoring"""
    await system_health_monitor.start_monitoring(interval_seconds)

def stop_health_monitoring():
    """Stop health monitoring"""
    system_health_monitor.stop_monitoring()

# Export main classes and functions
__all__ = [
    "AtlasSystemHealthMonitor",
    "SystemHealthReport",
    "HealthMetric",
    "HealthStatus",
    "system_health_monitor",
    "get_health_status",
    "generate_health_report",
    "start_health_monitoring",
    "stop_health_monitoring"
]
