"""
A.T.L.A.S. Mathematical Safeguards
Critical mathematical validation and error prevention for trading calculations
"""

import math
import logging
from typing import Union, Optional, Any
from decimal import Decimal, ROUND_HALF_UP, InvalidOperation
from dataclasses import dataclass

logger = logging.getLogger(__name__)

class MathematicalError(Exception):
    """Custom exception for mathematical validation errors"""
    pass

@dataclass
class ValidationResult:
    """Result of mathematical validation"""
    is_valid: bool
    value: Optional[Union[float, int, Decimal]]
    error_message: Optional[str] = None
    warning_message: Optional[str] = None

class AtlasMathSafeguards:
    """
    Mathematical safeguards for trading calculations
    Prevents overflow, underflow, division by zero, and invalid financial calculations
    """
    
    # Financial constants and limits
    MAX_PRICE = 1000000.0  # $1M max price
    MIN_PRICE = 0.0001     # $0.0001 min price
    MAX_VOLUME = 1000000000  # 1B max volume
    MIN_VOLUME = 1         # 1 min volume
    MAX_PERCENTAGE = 1000.0  # 1000% max percentage change
    MIN_PERCENTAGE = -100.0  # -100% min percentage change
    MAX_POSITION_SIZE = 10000000.0  # $10M max position
    MIN_POSITION_SIZE = 1.0  # $1 min position
    
    # Precision settings
    PRICE_PRECISION = 4
    PERCENTAGE_PRECISION = 2
    CURRENCY_PRECISION = 2
    
    @classmethod
    def validate_price(cls, price: Union[float, int, str]) -> ValidationResult:
        """Validate price values"""
        try:
            if price is None:
                return ValidationResult(False, None, "Price cannot be None")
            
            # Convert to float
            if isinstance(price, str):
                price = float(price)
            
            price = float(price)
            
            # Check for invalid values
            if math.isnan(price):
                return ValidationResult(False, None, "Price cannot be NaN")
            
            if math.isinf(price):
                return ValidationResult(False, None, "Price cannot be infinite")
            
            # Check bounds
            if price < cls.MIN_PRICE:
                return ValidationResult(False, None, f"Price {price} below minimum {cls.MIN_PRICE}")
            
            if price > cls.MAX_PRICE:
                return ValidationResult(False, None, f"Price {price} above maximum {cls.MAX_PRICE}")
            
            # Round to appropriate precision
            rounded_price = round(price, cls.PRICE_PRECISION)
            
            return ValidationResult(True, rounded_price)
            
        except (ValueError, TypeError, OverflowError) as e:
            return ValidationResult(False, None, f"Invalid price format: {e}")
    
    @classmethod
    def validate_volume(cls, volume: Union[float, int, str]) -> ValidationResult:
        """Validate volume values"""
        try:
            if volume is None:
                return ValidationResult(False, None, "Volume cannot be None")
            
            # Convert to int
            if isinstance(volume, str):
                volume = int(float(volume))
            
            volume = int(volume)
            
            # Check bounds
            if volume < cls.MIN_VOLUME:
                return ValidationResult(False, None, f"Volume {volume} below minimum {cls.MIN_VOLUME}")
            
            if volume > cls.MAX_VOLUME:
                return ValidationResult(False, None, f"Volume {volume} above maximum {cls.MAX_VOLUME}")
            
            return ValidationResult(True, volume)
            
        except (ValueError, TypeError, OverflowError) as e:
            return ValidationResult(False, None, f"Invalid volume format: {e}")
    
    @classmethod
    def validate_percentage(cls, percentage: Union[float, int, str]) -> ValidationResult:
        """Validate percentage values"""
        try:
            if percentage is None:
                return ValidationResult(False, None, "Percentage cannot be None")
            
            # Convert to float
            if isinstance(percentage, str):
                percentage = float(percentage)
            
            percentage = float(percentage)
            
            # Check for invalid values
            if math.isnan(percentage):
                return ValidationResult(False, None, "Percentage cannot be NaN")
            
            if math.isinf(percentage):
                return ValidationResult(False, None, "Percentage cannot be infinite")
            
            # Check bounds
            if percentage < cls.MIN_PERCENTAGE:
                return ValidationResult(False, None, f"Percentage {percentage} below minimum {cls.MIN_PERCENTAGE}")
            
            if percentage > cls.MAX_PERCENTAGE:
                return ValidationResult(False, None, f"Percentage {percentage} above maximum {cls.MAX_PERCENTAGE}")
            
            # Round to appropriate precision
            rounded_percentage = round(percentage, cls.PERCENTAGE_PRECISION)
            
            return ValidationResult(True, rounded_percentage)
            
        except (ValueError, TypeError, OverflowError) as e:
            return ValidationResult(False, None, f"Invalid percentage format: {e}")
    
    @classmethod
    def safe_divide(cls, numerator: float, denominator: float) -> ValidationResult:
        """Safe division with zero check"""
        try:
            if denominator == 0:
                return ValidationResult(False, None, "Division by zero")
            
            if abs(denominator) < 1e-10:
                return ValidationResult(False, None, "Division by near-zero value")
            
            result = numerator / denominator
            
            if math.isnan(result):
                return ValidationResult(False, None, "Division resulted in NaN")
            
            if math.isinf(result):
                return ValidationResult(False, None, "Division resulted in infinity")
            
            return ValidationResult(True, result)
            
        except (ZeroDivisionError, OverflowError) as e:
            return ValidationResult(False, None, f"Division error: {e}")
    
    @classmethod
    def calculate_percentage_change(cls, old_value: float, new_value: float) -> ValidationResult:
        """Calculate percentage change safely"""
        try:
            # Validate inputs
            old_validation = cls.validate_price(old_value)
            if not old_validation.is_valid:
                return old_validation
            
            new_validation = cls.validate_price(new_value)
            if not new_validation.is_valid:
                return new_validation
            
            old_value = old_validation.value
            new_value = new_validation.value
            
            # Calculate percentage change
            if old_value == 0:
                if new_value == 0:
                    return ValidationResult(True, 0.0)
                else:
                    return ValidationResult(False, None, "Cannot calculate percentage change from zero")
            
            percentage_change = ((new_value - old_value) / old_value) * 100
            
            return cls.validate_percentage(percentage_change)
            
        except Exception as e:
            return ValidationResult(False, None, f"Percentage change calculation error: {e}")
    
    @classmethod
    def calculate_position_value(cls, shares: int, price: float) -> ValidationResult:
        """Calculate position value safely"""
        try:
            # Validate inputs
            if shares <= 0:
                return ValidationResult(False, None, "Shares must be positive")
            
            price_validation = cls.validate_price(price)
            if not price_validation.is_valid:
                return price_validation
            
            price = price_validation.value
            
            # Calculate position value
            position_value = shares * price
            
            # Check bounds
            if position_value > cls.MAX_POSITION_SIZE:
                return ValidationResult(False, None, f"Position value {position_value} exceeds maximum {cls.MAX_POSITION_SIZE}")
            
            if position_value < cls.MIN_POSITION_SIZE:
                return ValidationResult(False, None, f"Position value {position_value} below minimum {cls.MIN_POSITION_SIZE}")
            
            # Round to currency precision
            rounded_value = round(position_value, cls.CURRENCY_PRECISION)
            
            return ValidationResult(True, rounded_value)
            
        except Exception as e:
            return ValidationResult(False, None, f"Position value calculation error: {e}")
    
    @classmethod
    def calculate_risk_reward_ratio(cls, entry_price: float, target_price: float, stop_loss: float) -> ValidationResult:
        """Calculate risk/reward ratio safely"""
        try:
            # Validate inputs
            entry_validation = cls.validate_price(entry_price)
            if not entry_validation.is_valid:
                return entry_validation
            
            target_validation = cls.validate_price(target_price)
            if not target_validation.is_valid:
                return target_validation
            
            stop_validation = cls.validate_price(stop_loss)
            if not stop_validation.is_valid:
                return stop_validation
            
            entry_price = entry_validation.value
            target_price = target_validation.value
            stop_loss = stop_validation.value
            
            # Calculate reward and risk
            reward = abs(target_price - entry_price)
            risk = abs(entry_price - stop_loss)
            
            # Calculate ratio
            ratio_result = cls.safe_divide(reward, risk)
            if not ratio_result.is_valid:
                return ratio_result
            
            ratio = ratio_result.value
            
            # Round to reasonable precision
            rounded_ratio = round(ratio, 2)
            
            return ValidationResult(True, rounded_ratio)
            
        except Exception as e:
            return ValidationResult(False, None, f"Risk/reward ratio calculation error: {e}")
    
    @classmethod
    def validate_trading_parameters(cls, entry_price: float, target_price: float, 
                                  stop_loss: float, position_size: float) -> ValidationResult:
        """Validate complete trading parameters"""
        try:
            # Validate all prices
            validations = [
                cls.validate_price(entry_price),
                cls.validate_price(target_price),
                cls.validate_price(stop_loss)
            ]
            
            for validation in validations:
                if not validation.is_valid:
                    return validation
            
            # Validate position size
            if position_size <= 0:
                return ValidationResult(False, None, "Position size must be positive")
            
            if position_size > cls.MAX_POSITION_SIZE:
                return ValidationResult(False, None, f"Position size {position_size} exceeds maximum")
            
            # Validate price relationships for long positions
            if target_price > entry_price:  # Long position
                if stop_loss >= entry_price:
                    return ValidationResult(False, None, "Stop loss must be below entry price for long positions")
            else:  # Short position
                if stop_loss <= entry_price:
                    return ValidationResult(False, None, "Stop loss must be above entry price for short positions")
            
            return ValidationResult(True, {
                "entry_price": entry_price,
                "target_price": target_price,
                "stop_loss": stop_loss,
                "position_size": position_size
            })
            
        except Exception as e:
            return ValidationResult(False, None, f"Trading parameters validation error: {e}")

# Global instance for easy access
math_safeguards = AtlasMathSafeguards()

# Convenience functions
def safe_float(value: Any, default: float = 0.0) -> float:
    """Safely convert value to float"""
    try:
        if value is None:
            return default
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int(value: Any, default: int = 0) -> int:
    """Safely convert value to int"""
    try:
        if value is None:
            return default
        return int(float(value))
    except (ValueError, TypeError):
        return default

def is_valid_number(value: Any) -> bool:
    """Check if value is a valid number"""
    try:
        float_val = float(value)
        return not (math.isnan(float_val) or math.isinf(float_val))
    except (ValueError, TypeError):
        return False

# Export main classes and functions
__all__ = [
    "AtlasMathSafeguards",
    "MathematicalError", 
    "ValidationResult",
    "math_safeguards",
    "safe_float",
    "safe_int", 
    "is_valid_number"
]
