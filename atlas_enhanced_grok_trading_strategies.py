"""
A.T.L.A.S. Enhanced Grok Trading Strategies
Advanced AI-powered trading strategies using Grok integration
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

# Core imports
from atlas_grok_integration import AtlasGrokIntegrationEngine, GrokTaskType, GrokCapability
from atlas_enhanced_market_data import AtlasEnhancedMarketDataEngine
from atlas_lee_method import AtlasLeeMethodRealtimeScanner

logger = logging.getLogger(__name__)

class StrategyType(Enum):
    """Types of enhanced trading strategies"""
    MOMENTUM_BREAKOUT = "momentum_breakout"
    MEAN_REVERSION = "mean_reversion"
    TREND_FOLLOWING = "trend_following"
    VOLATILITY_BREAKOUT = "volatility_breakout"
    NEWS_SENTIMENT = "news_sentiment"
    PATTERN_RECOGNITION = "pattern_recognition"
    MULTI_TIMEFRAME = "multi_timeframe"
    RISK_PARITY = "risk_parity"

@dataclass
class StrategySignal:
    """Enhanced trading strategy signal"""
    symbol: str
    strategy_type: StrategyType
    signal_strength: float  # 0.0 to 1.0
    direction: str  # "BUY", "SELL", "HOLD"
    confidence: float
    entry_price: float
    target_price: float
    stop_loss: float
    position_size: int
    reasoning: List[str]
    grok_analysis: Dict[str, Any]
    timestamp: datetime

class AtlasEnhancedGrokTradingStrategies:
    """
    Enhanced trading strategies powered by Grok AI integration
    Combines traditional technical analysis with advanced AI insights
    """
    
    def __init__(self):
        self.grok_engine = AtlasGrokIntegrationEngine()
        self.market_data_engine = AtlasEnhancedMarketDataEngine()
        self.lee_method_scanner = AtlasLeeMethodRealtimeScanner()
        
        # Strategy configurations
        self.strategy_configs = self._setup_strategy_configs()
        
        logger.info("Enhanced Grok trading strategies initialized")
    
    async def initialize(self):
        """Initialize all components"""
        try:
            await self.grok_engine.initialize()
            await self.market_data_engine.initialize()
            await self.lee_method_scanner.initialize()
            
            logger.info("Enhanced Grok trading strategies ready")
            
        except Exception as e:
            logger.error(f"Failed to initialize enhanced strategies: {e}")
            raise
    
    async def analyze_symbol(self, symbol: str, strategy_types: List[StrategyType] = None) -> List[StrategySignal]:
        """
        Analyze a symbol using multiple enhanced strategies
        """
        try:
            if strategy_types is None:
                strategy_types = list(StrategyType)
            
            signals = []
            
            # Get market data
            market_data = await self.market_data_engine.get_real_time_quote(symbol)
            if not market_data:
                logger.warning(f"No market data available for {symbol}")
                return []
            
            # Run each strategy
            for strategy_type in strategy_types:
                try:
                    signal = await self._execute_strategy(symbol, strategy_type, market_data)
                    if signal:
                        signals.append(signal)
                except Exception as e:
                    logger.error(f"Strategy {strategy_type.value} failed for {symbol}: {e}")
                    continue
            
            # Rank signals by strength and confidence
            signals.sort(key=lambda x: x.signal_strength * x.confidence, reverse=True)
            
            return signals
            
        except Exception as e:
            logger.error(f"Symbol analysis failed for {symbol}: {e}")
            return []
    
    async def _execute_strategy(self, symbol: str, strategy_type: StrategyType, 
                              market_data: Dict[str, Any]) -> Optional[StrategySignal]:
        """
        Execute a specific strategy
        """
        try:
            if strategy_type == StrategyType.MOMENTUM_BREAKOUT:
                return await self._momentum_breakout_strategy(symbol, market_data)
            elif strategy_type == StrategyType.MEAN_REVERSION:
                return await self._mean_reversion_strategy(symbol, market_data)
            elif strategy_type == StrategyType.TREND_FOLLOWING:
                return await self._trend_following_strategy(symbol, market_data)
            elif strategy_type == StrategyType.VOLATILITY_BREAKOUT:
                return await self._volatility_breakout_strategy(symbol, market_data)
            elif strategy_type == StrategyType.NEWS_SENTIMENT:
                return await self._news_sentiment_strategy(symbol, market_data)
            elif strategy_type == StrategyType.PATTERN_RECOGNITION:
                return await self._pattern_recognition_strategy(symbol, market_data)
            elif strategy_type == StrategyType.MULTI_TIMEFRAME:
                return await self._multi_timeframe_strategy(symbol, market_data)
            elif strategy_type == StrategyType.RISK_PARITY:
                return await self._risk_parity_strategy(symbol, market_data)
            else:
                logger.warning(f"Unknown strategy type: {strategy_type}")
                return None
                
        except Exception as e:
            logger.error(f"Strategy execution failed: {e}")
            return None
    
    async def _momentum_breakout_strategy(self, symbol: str, market_data: Dict[str, Any]) -> Optional[StrategySignal]:
        """
        Momentum breakout strategy with Grok enhancement
        """
        try:
            current_price = market_data.get("price", 0)
            volume = market_data.get("volume", 0)
            
            # Get Grok analysis for momentum
            grok_prompt = f"""
            Analyze {symbol} for momentum breakout potential:
            Current Price: ${current_price}
            Volume: {volume:,}
            
            Evaluate:
            1. Price momentum indicators
            2. Volume confirmation
            3. Breakout probability
            4. Risk factors
            
            Provide specific entry, target, and stop loss levels.
            """
            
            grok_response = await self.grok_engine.analyze_market_data(
                symbol, grok_prompt, GrokTaskType.TECHNICAL_ANALYSIS
            )
            
            if not grok_response or not grok_response.success:
                return None
            
            # Extract Grok insights
            grok_analysis = grok_response.analysis_result
            
            # Calculate signal strength based on momentum indicators
            signal_strength = self._calculate_momentum_strength(market_data, grok_analysis)
            
            # Determine direction and confidence
            direction = "BUY" if signal_strength > 0.6 else "HOLD"
            confidence = min(signal_strength * 1.2, 1.0)
            
            # Calculate prices
            entry_price = current_price * 1.002  # Slight premium for breakout
            target_price = current_price * 1.08   # 8% target
            stop_loss = current_price * 0.95      # 5% stop loss
            
            # Position sizing
            position_size = self._calculate_position_size(current_price, stop_loss, 10000)
            
            # Generate reasoning
            reasoning = [
                f"Momentum breakout analysis for {symbol}",
                f"Signal strength: {signal_strength:.2f}",
                f"Grok confidence: {grok_analysis.get('confidence', 0.5):.2f}",
                "Volume confirmation required for entry"
            ]
            
            return StrategySignal(
                symbol=symbol,
                strategy_type=StrategyType.MOMENTUM_BREAKOUT,
                signal_strength=signal_strength,
                direction=direction,
                confidence=confidence,
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                position_size=position_size,
                reasoning=reasoning,
                grok_analysis=grok_analysis,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Momentum breakout strategy failed for {symbol}: {e}")
            return None
    
    async def _pattern_recognition_strategy(self, symbol: str, market_data: Dict[str, Any]) -> Optional[StrategySignal]:
        """
        Pattern recognition strategy using Lee Method and Grok
        """
        try:
            # Get Lee Method analysis
            lee_signals = await self.lee_method_scanner.scan_symbol(symbol)
            
            if not lee_signals:
                return None
            
            # Get the strongest Lee Method signal
            strongest_signal = max(lee_signals, key=lambda x: x.strength)
            
            # Get Grok pattern analysis
            grok_prompt = f"""
            Advanced pattern analysis for {symbol}:
            
            Lee Method Signal:
            - Pattern: {strongest_signal.pattern_type}
            - Strength: {strongest_signal.strength}
            - Criteria Met: {strongest_signal.criteria_met}/3
            
            Current Market Data:
            - Price: ${market_data.get('price', 0)}
            - Volume: {market_data.get('volume', 0):,}
            - Change: {market_data.get('change_percent', 0):.2f}%
            
            Provide:
            1. Pattern validation and confirmation
            2. Entry timing optimization
            3. Risk assessment
            4. Price targets based on pattern completion
            """
            
            grok_response = await self.grok_engine.analyze_market_data(
                symbol, grok_prompt, GrokTaskType.PATTERN_ANALYSIS
            )
            
            if not grok_response or not grok_response.success:
                return None
            
            grok_analysis = grok_response.analysis_result
            
            # Calculate signal strength
            lee_strength = strongest_signal.strength / 5.0  # Convert 1-5 to 0-1
            grok_confidence = grok_analysis.get('confidence', 0.5)
            signal_strength = (lee_strength + grok_confidence) / 2
            
            # Determine direction
            direction = "BUY" if strongest_signal.signal_type == "bullish" else "SELL"
            if signal_strength < 0.5:
                direction = "HOLD"
            
            current_price = market_data.get("price", 0)
            
            # Calculate prices based on pattern
            if direction == "BUY":
                entry_price = current_price * 1.001
                target_price = current_price * 1.12  # 12% target for strong patterns
                stop_loss = current_price * 0.94     # 6% stop loss
            else:
                entry_price = current_price * 0.999
                target_price = current_price * 0.88  # 12% target for short
                stop_loss = current_price * 1.06     # 6% stop loss
            
            position_size = self._calculate_position_size(current_price, stop_loss, 10000)
            
            reasoning = [
                f"Lee Method pattern detected: {strongest_signal.pattern_type}",
                f"Pattern strength: {strongest_signal.strength}/5",
                f"Criteria met: {strongest_signal.criteria_met}/3",
                f"Grok validation confidence: {grok_confidence:.2f}",
                f"Combined signal strength: {signal_strength:.2f}"
            ]
            
            return StrategySignal(
                symbol=symbol,
                strategy_type=StrategyType.PATTERN_RECOGNITION,
                signal_strength=signal_strength,
                direction=direction,
                confidence=signal_strength,
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                position_size=position_size,
                reasoning=reasoning,
                grok_analysis=grok_analysis,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Pattern recognition strategy failed for {symbol}: {e}")
            return None
    
    async def _news_sentiment_strategy(self, symbol: str, market_data: Dict[str, Any]) -> Optional[StrategySignal]:
        """
        News sentiment-based strategy with Grok analysis
        """
        try:
            # Get Grok news sentiment analysis
            grok_prompt = f"""
            Analyze recent news sentiment for {symbol}:
            
            Current Price: ${market_data.get('price', 0)}
            Volume: {market_data.get('volume', 0):,}
            
            Evaluate:
            1. Recent news sentiment (last 24 hours)
            2. Market reaction to news
            3. Sentiment momentum
            4. Trading opportunities based on sentiment
            
            Provide sentiment score (-1 to +1) and trading recommendation.
            """
            
            grok_response = await self.grok_engine.analyze_market_sentiment(
                symbol, grok_prompt
            )
            
            if not grok_response or not grok_response.success:
                return None
            
            grok_analysis = grok_response.analysis_result
            sentiment_score = grok_analysis.get('sentiment_score', 0.0)
            
            # Calculate signal strength based on sentiment
            signal_strength = abs(sentiment_score)
            
            # Determine direction
            if sentiment_score > 0.3:
                direction = "BUY"
            elif sentiment_score < -0.3:
                direction = "SELL"
            else:
                direction = "HOLD"
            
            current_price = market_data.get("price", 0)
            
            # Calculate prices
            if direction == "BUY":
                entry_price = current_price * 1.002
                target_price = current_price * (1 + sentiment_score * 0.1)  # Scale target by sentiment
                stop_loss = current_price * 0.96
            elif direction == "SELL":
                entry_price = current_price * 0.998
                target_price = current_price * (1 + sentiment_score * 0.1)  # Negative sentiment
                stop_loss = current_price * 1.04
            else:
                entry_price = current_price
                target_price = current_price
                stop_loss = current_price * 0.97
            
            position_size = self._calculate_position_size(current_price, stop_loss, 10000)
            
            reasoning = [
                f"News sentiment analysis for {symbol}",
                f"Sentiment score: {sentiment_score:.2f}",
                f"Signal strength: {signal_strength:.2f}",
                f"Direction: {direction}",
                "Based on recent news and market reaction"
            ]
            
            return StrategySignal(
                symbol=symbol,
                strategy_type=StrategyType.NEWS_SENTIMENT,
                signal_strength=signal_strength,
                direction=direction,
                confidence=signal_strength,
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                position_size=position_size,
                reasoning=reasoning,
                grok_analysis=grok_analysis,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"News sentiment strategy failed for {symbol}: {e}")
            return None
    
    def _calculate_momentum_strength(self, market_data: Dict[str, Any], 
                                   grok_analysis: Dict[str, Any]) -> float:
        """
        Calculate momentum strength from market data and Grok analysis
        """
        try:
            # Basic momentum indicators
            change_percent = abs(market_data.get("change_percent", 0)) / 100.0
            volume_ratio = min(market_data.get("volume", 0) / 1000000, 1.0)  # Normalize volume
            
            # Grok confidence
            grok_confidence = grok_analysis.get('confidence', 0.5)
            
            # Combine factors
            momentum_strength = (change_percent * 0.4 + volume_ratio * 0.3 + grok_confidence * 0.3)
            
            return min(momentum_strength, 1.0)
            
        except Exception as e:
            logger.error(f"Momentum strength calculation failed: {e}")
            return 0.0
    
    def _calculate_position_size(self, entry_price: float, stop_loss: float, 
                               account_value: float, risk_percent: float = 0.02) -> int:
        """
        Calculate position size based on risk management
        """
        try:
            risk_amount = account_value * risk_percent
            price_risk = abs(entry_price - stop_loss)
            
            if price_risk > 0:
                position_value = risk_amount / (price_risk / entry_price)
                shares = int(position_value / entry_price)
                return max(1, shares)
            
            return 1
            
        except Exception as e:
            logger.error(f"Position size calculation failed: {e}")
            return 1
    
    def _setup_strategy_configs(self) -> Dict[StrategyType, Dict[str, Any]]:
        """
        Setup configuration for each strategy type
        """
        return {
            StrategyType.MOMENTUM_BREAKOUT: {
                'min_volume': 100000,
                'min_price_change': 0.02,
                'target_multiplier': 1.08,
                'stop_multiplier': 0.95
            },
            StrategyType.PATTERN_RECOGNITION: {
                'min_lee_strength': 3,
                'min_criteria_met': 2,
                'target_multiplier': 1.12,
                'stop_multiplier': 0.94
            },
            StrategyType.NEWS_SENTIMENT: {
                'min_sentiment_threshold': 0.3,
                'sentiment_multiplier': 0.1,
                'target_base': 1.06,
                'stop_multiplier': 0.96
            }
        }
    
    # Placeholder methods for other strategies
    async def _mean_reversion_strategy(self, symbol: str, market_data: Dict[str, Any]) -> Optional[StrategySignal]:
        """Mean reversion strategy (placeholder)"""
        return None
    
    async def _trend_following_strategy(self, symbol: str, market_data: Dict[str, Any]) -> Optional[StrategySignal]:
        """Trend following strategy (placeholder)"""
        return None
    
    async def _volatility_breakout_strategy(self, symbol: str, market_data: Dict[str, Any]) -> Optional[StrategySignal]:
        """Volatility breakout strategy (placeholder)"""
        return None
    
    async def _multi_timeframe_strategy(self, symbol: str, market_data: Dict[str, Any]) -> Optional[StrategySignal]:
        """Multi-timeframe strategy (placeholder)"""
        return None
    
    async def _risk_parity_strategy(self, symbol: str, market_data: Dict[str, Any]) -> Optional[StrategySignal]:
        """Risk parity strategy (placeholder)"""
        return None

# Global enhanced strategy instance
enhanced_grok_strategy = AtlasEnhancedGrokTradingStrategies()

# Convenience functions
async def analyze_symbol_with_grok(symbol: str) -> List[StrategySignal]:
    """Analyze symbol using all enhanced strategies"""
    return await enhanced_grok_strategy.analyze_symbol(symbol)

async def get_momentum_signal(symbol: str) -> Optional[StrategySignal]:
    """Get momentum breakout signal for symbol"""
    signals = await enhanced_grok_strategy.analyze_symbol(symbol, [StrategyType.MOMENTUM_BREAKOUT])
    return signals[0] if signals else None

# Export main classes and functions
__all__ = [
    "AtlasEnhancedGrokTradingStrategies",
    "StrategyType",
    "StrategySignal",
    "enhanced_grok_strategy",
    "analyze_symbol_with_grok",
    "get_momentum_signal"
]
