"""
A.T.L.A.S. Web Search Service
Enhanced web search capabilities for AI context and news analysis
"""

import asyncio
import logging
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum

# Core imports
from config import get_api_config, settings

logger = logging.getLogger(__name__)

class SearchContext(Enum):
    """Context for web search requests"""
    AI_CONVERSATION = "ai_conversation"
    NEWS_SENTIMENT = "news_sentiment"
    MARKET_ANALYSIS = "market_analysis"
    TECHNICAL_ANALYSIS = "technical_analysis"
    EARNINGS_RESEARCH = "earnings_research"
    GENERAL_RESEARCH = "general_research"

@dataclass
class SearchQuery:
    """Web search query structure"""
    query: str
    context: SearchContext
    symbols: List[str] = None
    timeframe: str = "1d"  # 1d, 1w, 1m
    max_results: int = 5
    language: str = "en"
    region: str = "us"

@dataclass
class SearchResult:
    """Web search result structure"""
    title: str
    url: str
    snippet: str
    source: str
    published_date: Optional[datetime] = None
    relevance_score: float = 0.0
    symbols_mentioned: List[str] = None
    sentiment_score: Optional[float] = None

class AtlasWebSearchService:
    """
    Web search service for enhanced AI capabilities and market research
    Provides contextual search results for trading analysis
    """
    
    def __init__(self):
        self.search_configs = {
            "google": get_api_config("google_search"),
            "bing": get_api_config("bing_search"),
            "serp": get_api_config("serp"),
            "serpapi": get_api_config("serpapi")
        }
        self.is_enabled = getattr(settings, 'WEB_SEARCH_ENABLED', False)
        self.cache = {}
        self.cache_ttl = 3600  # 1 hour cache

        # Rate limiting
        self.last_search_time = 0
        self.min_search_interval = 1.0  # 1 second between searches

        # Available search providers
        self.available_providers = self._check_available_providers()

        logger.info(f"Web search service initialized - Enabled: {self.is_enabled}, Providers: {self.available_providers}")
    
    def _check_available_providers(self) -> List[str]:
        """Check which search providers are available"""
        available = []

        for provider, config in self.search_configs.items():
            if config.get('available', False) and config.get('api_key'):
                available.append(provider)

        # DuckDuckGo is always available (no API key required)
        if getattr(settings, 'DUCKDUCKGO_ENABLED', True):
            available.append('duckduckgo')

        return available

    def is_available(self) -> bool:
        """Check if web search service is available"""
        return self.is_enabled and len(self.available_providers) > 0
    
    async def search(self, search_query: SearchQuery) -> List[SearchResult]:
        """
        Perform web search with context awareness
        """
        try:
            if not self.is_available():
                logger.warning("Web search service not available")
                return []
            
            # Check cache first
            cache_key = self._generate_cache_key(search_query)
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                logger.debug(f"Returning cached search results for: {search_query.query[:50]}...")
                return cached_result
            
            # Rate limiting
            await self._enforce_rate_limit()
            
            # Enhance query based on context
            enhanced_query = self._enhance_query(search_query)
            
            # Perform search (mock implementation for now)
            results = await self._perform_search(enhanced_query, search_query)
            
            # Process and filter results
            processed_results = self._process_results(results, search_query)
            
            # Cache results
            self._cache_results(cache_key, processed_results)
            
            logger.info(f"Web search completed: {len(processed_results)} results for '{search_query.query[:50]}...'")
            return processed_results
            
        except Exception as e:
            logger.error(f"Web search failed: {e}")
            return []
    
    async def search_for_context(self, query: str, context: SearchContext, 
                                symbols: List[str] = None, max_results: int = 3) -> List[SearchResult]:
        """
        Convenience method for contextual search
        """
        search_query = SearchQuery(
            query=query,
            context=context,
            symbols=symbols or [],
            max_results=max_results
        )
        
        return await self.search(search_query)
    
    def _enhance_query(self, search_query: SearchQuery) -> str:
        """
        Enhance search query based on context and symbols
        """
        base_query = search_query.query
        enhanced_parts = [base_query]
        
        # Add symbols to query
        if search_query.symbols:
            symbols_str = " ".join(search_query.symbols[:3])  # Limit to 3 symbols
            enhanced_parts.append(symbols_str)
        
        # Add context-specific terms
        context_terms = {
            SearchContext.AI_CONVERSATION: ["stock market", "trading"],
            SearchContext.NEWS_SENTIMENT: ["news", "market sentiment", "analysis"],
            SearchContext.MARKET_ANALYSIS: ["market analysis", "stock performance"],
            SearchContext.TECHNICAL_ANALYSIS: ["technical analysis", "chart patterns"],
            SearchContext.EARNINGS_RESEARCH: ["earnings", "financial results", "quarterly"],
            SearchContext.GENERAL_RESEARCH: ["investment", "financial"]
        }
        
        if search_query.context in context_terms:
            enhanced_parts.extend(context_terms[search_query.context])
        
        # Add timeframe filter
        if search_query.timeframe == "1d":
            enhanced_parts.append("today")
        elif search_query.timeframe == "1w":
            enhanced_parts.append("this week")
        elif search_query.timeframe == "1m":
            enhanced_parts.append("this month")
        
        enhanced_query = " ".join(enhanced_parts)
        logger.debug(f"Enhanced query: '{base_query}' -> '{enhanced_query}'")
        
        return enhanced_query
    
    async def _perform_search(self, query: str, search_query: SearchQuery) -> List[Dict[str, Any]]:
        """
        Perform the actual web search using available providers
        """
        try:
            results = []

            # Try providers in order of preference
            for provider in self.available_providers:
                try:
                    if provider == "duckduckgo":
                        provider_results = await self._search_duckduckgo(query, search_query.max_results)
                    elif provider == "google":
                        provider_results = await self._search_google(query, search_query.max_results)
                    elif provider == "serpapi":
                        provider_results = await self._search_serpapi(query, search_query.max_results)
                    elif provider == "serp":
                        provider_results = await self._search_serp_api(query, search_query.max_results)
                    else:
                        continue

                    if provider_results:
                        results.extend(provider_results)
                        break  # Use first successful provider

                except Exception as e:
                    logger.warning(f"Search provider {provider} failed: {e}")
                    continue

            # Fallback to mock data if no real results
            if not results:
                logger.warning("All search providers failed, using fallback mock data")
                results = await self._get_mock_results(query, search_query)

            return results[:search_query.max_results]

        except Exception as e:
            logger.error(f"Search execution failed: {e}")
            return []

    async def _search_duckduckgo(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """Search using DuckDuckGo (no API key required)"""
        try:
            from duckduckgo_search import DDGS

            results = []
            with DDGS() as ddgs:
                search_results = ddgs.text(query, max_results=max_results)

                for result in search_results:
                    results.append({
                        "title": result.get("title", ""),
                        "url": result.get("href", ""),
                        "snippet": result.get("body", ""),
                        "source": "DuckDuckGo",
                        "published_date": datetime.now()  # DDG doesn't provide dates
                    })

            return results

        except ImportError:
            logger.warning("duckduckgo-search package not installed")
            return []
        except Exception as e:
            logger.error(f"DuckDuckGo search failed: {e}")
            return []

    async def _search_google(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """Search using Google Custom Search API"""
        try:
            import aiohttp

            config = self.search_configs["google"]
            api_key = config.get("api_key")
            search_engine_id = config.get("search_engine_id")

            if not api_key or not search_engine_id:
                return []

            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                "key": api_key,
                "cx": search_engine_id,
                "q": query,
                "num": min(max_results, 10)  # Google allows max 10
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = []

                        for item in data.get("items", []):
                            results.append({
                                "title": item.get("title", ""),
                                "url": item.get("link", ""),
                                "snippet": item.get("snippet", ""),
                                "source": "Google",
                                "published_date": datetime.now()
                            })

                        return results

            return []

        except Exception as e:
            logger.error(f"Google search failed: {e}")
            return []

    async def _search_serpapi(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """Search using SerpAPI"""
        try:
            import aiohttp

            config = self.search_configs["serpapi"]
            api_key = config.get("api_key")

            if not api_key:
                return []

            url = "https://serpapi.com/search"
            params = {
                "api_key": api_key,
                "engine": "google",
                "q": query,
                "num": max_results
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = []

                        for item in data.get("organic_results", []):
                            results.append({
                                "title": item.get("title", ""),
                                "url": item.get("link", ""),
                                "snippet": item.get("snippet", ""),
                                "source": "SerpAPI",
                                "published_date": datetime.now()
                            })

                        return results

            return []

        except Exception as e:
            logger.error(f"SerpAPI search failed: {e}")
            return []

    async def _search_serp_api(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """Search using SERP API"""
        try:
            import aiohttp

            config = self.search_configs["serp"]
            api_key = config.get("api_key")

            if not api_key:
                return []

            url = "https://api.serpapi.com/search"
            params = {
                "api_key": api_key,
                "q": query,
                "num": max_results
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = []

                        for item in data.get("results", []):
                            results.append({
                                "title": item.get("title", ""),
                                "url": item.get("url", ""),
                                "snippet": item.get("description", ""),
                                "source": "SERP API",
                                "published_date": datetime.now()
                            })

                        return results

            return []

        except Exception as e:
            logger.error(f"SERP API search failed: {e}")
            return []

    async def _get_mock_results(self, query: str, search_query: SearchQuery) -> List[Dict[str, Any]]:
        """Fallback mock results when no real search providers are available"""
        mock_results = [
            {
                "title": f"Market Analysis: {search_query.symbols[0] if search_query.symbols else 'Stock'} Performance Today",
                "url": "https://example.com/market-analysis",
                "snippet": f"Latest analysis shows strong performance indicators for {query}. Technical analysis suggests continued momentum with volume confirmation.",
                "source": "MarketWatch (Mock)",
                "published_date": datetime.now() - timedelta(hours=2)
            },
            {
                "title": f"Breaking: {query} News Update",
                "url": "https://example.com/news-update",
                "snippet": f"Recent developments in {query} show positive market sentiment with institutional buying interest.",
                "source": "Financial Times (Mock)",
                "published_date": datetime.now() - timedelta(hours=4)
            },
            {
                "title": f"Technical Analysis: {query} Chart Patterns",
                "url": "https://example.com/technical-analysis",
                "snippet": f"Chart analysis reveals bullish patterns in {query} with strong support levels and breakout potential.",
                "source": "TradingView (Mock)",
                "published_date": datetime.now() - timedelta(hours=6)
            }
        ]

        return mock_results
    
    def _process_results(self, raw_results: List[Dict[str, Any]], 
                        search_query: SearchQuery) -> List[SearchResult]:
        """
        Process and filter search results
        """
        processed_results = []
        
        for result in raw_results:
            try:
                # Extract symbols mentioned in title/snippet
                symbols_mentioned = self._extract_symbols(
                    f"{result.get('title', '')} {result.get('snippet', '')}"
                )
                
                # Calculate relevance score
                relevance_score = self._calculate_relevance(result, search_query)
                
                # Estimate sentiment score
                sentiment_score = self._estimate_sentiment(result.get('snippet', ''))
                
                search_result = SearchResult(
                    title=result.get('title', ''),
                    url=result.get('url', ''),
                    snippet=result.get('snippet', ''),
                    source=result.get('source', 'Unknown'),
                    published_date=result.get('published_date'),
                    relevance_score=relevance_score,
                    symbols_mentioned=symbols_mentioned,
                    sentiment_score=sentiment_score
                )
                
                processed_results.append(search_result)
                
            except Exception as e:
                logger.error(f"Error processing search result: {e}")
                continue
        
        # Sort by relevance score
        processed_results.sort(key=lambda x: x.relevance_score, reverse=True)
        
        return processed_results
    
    def _extract_symbols(self, text: str) -> List[str]:
        """
        Extract stock symbols from text
        """
        import re
        
        # Simple regex for stock symbols (1-5 uppercase letters)
        symbol_pattern = r'\b[A-Z]{1,5}\b'
        potential_symbols = re.findall(symbol_pattern, text)
        
        # Filter out common words that aren't symbols
        common_words = {'THE', 'AND', 'FOR', 'ARE', 'BUT', 'NOT', 'YOU', 'ALL', 'CAN', 'HER', 'WAS', 'ONE', 'OUR', 'HAD', 'BY', 'UP', 'DO', 'NO', 'IF', 'MY', 'ON', 'AS', 'WE', 'TO', 'BE', 'AT', 'OR', 'IN', 'IS', 'IT', 'OF', 'SO', 'HE', 'HIS', 'SHE', 'HAS', 'AN'}
        
        symbols = [s for s in potential_symbols if s not in common_words and len(s) <= 5]
        
        return list(set(symbols))  # Remove duplicates
    
    def _calculate_relevance(self, result: Dict[str, Any], search_query: SearchQuery) -> float:
        """
        Calculate relevance score for search result
        """
        score = 0.0
        
        title = result.get('title', '').lower()
        snippet = result.get('snippet', '').lower()
        query_terms = search_query.query.lower().split()
        
        # Title relevance (higher weight)
        for term in query_terms:
            if term in title:
                score += 0.3
        
        # Snippet relevance
        for term in query_terms:
            if term in snippet:
                score += 0.1
        
        # Symbol relevance
        if search_query.symbols:
            for symbol in search_query.symbols:
                if symbol.lower() in title or symbol.lower() in snippet:
                    score += 0.2
        
        # Source credibility bonus
        credible_sources = ['reuters', 'bloomberg', 'marketwatch', 'cnbc', 'wsj', 'ft.com']
        source = result.get('source', '').lower()
        if any(credible in source for credible in credible_sources):
            score += 0.1
        
        # Recency bonus
        published_date = result.get('published_date')
        if published_date:
            hours_ago = (datetime.now() - published_date).total_seconds() / 3600
            if hours_ago < 24:  # Within 24 hours
                score += 0.1 * (1 - hours_ago / 24)
        
        return min(score, 1.0)  # Cap at 1.0
    
    def _estimate_sentiment(self, text: str) -> float:
        """
        Simple sentiment estimation (placeholder)
        In production, use proper sentiment analysis
        """
        positive_words = ['bullish', 'positive', 'strong', 'growth', 'gain', 'up', 'rise', 'increase', 'buy', 'outperform']
        negative_words = ['bearish', 'negative', 'weak', 'decline', 'loss', 'down', 'fall', 'decrease', 'sell', 'underperform']
        
        text_lower = text.lower()
        
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count + negative_count == 0:
            return 0.0  # Neutral
        
        sentiment = (positive_count - negative_count) / (positive_count + negative_count)
        return sentiment  # Range: -1 (negative) to 1 (positive)
    
    def _generate_cache_key(self, search_query: SearchQuery) -> str:
        """Generate cache key for search query"""
        query_dict = asdict(search_query)
        query_str = json.dumps(query_dict, sort_keys=True)
        return hashlib.md5(query_str.encode()).hexdigest()
    
    def _get_cached_result(self, cache_key: str) -> Optional[List[SearchResult]]:
        """Get cached search result if still valid"""
        if cache_key in self.cache:
            cached_data = self.cache[cache_key]
            if datetime.now() - cached_data['timestamp'] < timedelta(seconds=self.cache_ttl):
                return cached_data['results']
            else:
                # Remove expired cache entry
                del self.cache[cache_key]
        
        return None
    
    def _cache_results(self, cache_key: str, results: List[SearchResult]):
        """Cache search results"""
        self.cache[cache_key] = {
            'results': results,
            'timestamp': datetime.now()
        }
        
        # Clean old cache entries (keep only last 100)
        if len(self.cache) > 100:
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k]['timestamp'])
            del self.cache[oldest_key]
    
    async def _enforce_rate_limit(self):
        """Enforce rate limiting between searches"""
        current_time = asyncio.get_event_loop().time()
        time_since_last = current_time - self.last_search_time
        
        if time_since_last < self.min_search_interval:
            sleep_time = self.min_search_interval - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_search_time = asyncio.get_event_loop().time()
    
    def clear_cache(self):
        """Clear search cache"""
        self.cache.clear()
        logger.info("Web search cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'cache_size': len(self.cache),
            'cache_ttl': self.cache_ttl,
            'is_available': self.is_available(),
            'is_enabled': self.is_enabled
        }

# Global web search service instance
web_search_service = AtlasWebSearchService()

# Convenience functions
async def search_market_news(query: str, symbols: List[str] = None) -> List[SearchResult]:
    """Search for market news"""
    return await web_search_service.search_for_context(
        query, SearchContext.NEWS_SENTIMENT, symbols
    )

async def search_technical_analysis(symbol: str) -> List[SearchResult]:
    """Search for technical analysis"""
    return await web_search_service.search_for_context(
        f"{symbol} technical analysis", SearchContext.TECHNICAL_ANALYSIS, [symbol]
    )

# Export main classes and functions
__all__ = [
    "AtlasWebSearchService",
    "SearchContext",
    "SearchQuery", 
    "SearchResult",
    "web_search_service",
    "search_market_news",
    "search_technical_analysis"
]
