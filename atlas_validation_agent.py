"""
A.T.L.A.S. Validation Agent
Quality control supervisor for multi-agent system validation
"""

import logging
from datetime import datetime
from typing import Dict, List, Any
from dataclasses import dataclass

from atlas_multi_agent_core import (
    AtlasBaseAgent, AgentRole, AgentCapabilities, MultiAgentTask
)

logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Validation result structure"""
    component: str
    is_valid: bool
    confidence: float
    issues: List[str]
    recommendations: List[str]
    quality_score: float
    timestamp: datetime

class AtlasValidationAgent(AtlasBaseAgent):
    """Validation agent for quality control and system validation"""

    def __init__(self):
        super().__init__(AgentRole.VALIDATION_SUPERVISOR)
        self.validation_rules = self._setup_validation_rules()

    def _define_capabilities(self) -> AgentCapabilities:
        """Define validation agent capabilities"""
        return AgentCapabilities(
            role=AgentRole.VALIDATION_SUPERVISOR,
            supported_tasks=[
                "validate_trading_recommendation",
                "validate_analysis_results",
                "validate_risk_assessment",
                "validate_pattern_detection",
                "cross_validate_agents",
                "system_health_check"
            ],
            max_concurrent_tasks=5,
            average_processing_time=10.0,
            success_rate=0.98,
            dependencies=[]  # Validation agent has no dependencies
        )

    async def _initialize_agent(self):
        """Initialize the validation agent"""
        try:
            self.logger.info("Validation agent initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize validation agent: {e}")
            raise

    async def execute_task(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Execute a validation task"""
        try:
            task_type = task.input_data.get("task_type", "validate_trading_recommendation")

            if task_type == "validate_trading_recommendation":
                return await self._validate_trading_recommendation(task.input_data)
            elif task_type == "validate_analysis_results":
                return await self._validate_analysis_results(task.input_data)
            elif task_type == "validate_risk_assessment":
                return await self._validate_risk_assessment(task.input_data)
            elif task_type == "validate_pattern_detection":
                return await self._validate_pattern_detection(task.input_data)
            elif task_type == "cross_validate_agents":
                return await self._cross_validate_agents(task.input_data)
            elif task_type == "system_health_check":
                return await self._system_health_check(task.input_data)
            else:
                return {"error": f"Unknown task type: {task_type}"}

        except Exception as e:
            self.logger.error(f"Validation task failed: {e}")
            self.failed_tasks += 1
            return {"error": str(e)}

    async def _validate_trading_recommendation(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate a trading recommendation from the trade execution agent"""
        try:
            recommendation = data.get("recommendation", {})
            analysis = data.get("analysis", {})
            symbol = data.get("symbol", "")

            issues = []
            quality_score = 100.0

            # Validate recommendation structure
            required_fields = ["action", "entry_price", "target_price", "stop_loss", "position_size", "confidence"]
            for field in required_fields:
                if field not in recommendation:
                    issues.append(f"Missing required field: {field}")
                    quality_score -= 15

            # Validate price relationships
            action = recommendation.get("action", "")
            entry_price = recommendation.get("entry_price", 0)
            target_price = recommendation.get("target_price", 0)
            stop_loss = recommendation.get("stop_loss", 0)

            if action in ["BUY", "STRONG_BUY"]:
                if target_price <= entry_price:
                    issues.append("Target price must be above entry price for buy orders")
                    quality_score -= 20
                if stop_loss >= entry_price:
                    issues.append("Stop loss must be below entry price for buy orders")
                    quality_score -= 20

            elif action in ["SELL", "STRONG_SELL"]:
                if target_price >= entry_price:
                    issues.append("Target price must be below entry price for sell orders")
                    quality_score -= 20
                if stop_loss <= entry_price:
                    issues.append("Stop loss must be above entry price for sell orders")
                    quality_score -= 20

            # Validate risk/reward ratio
            risk_reward_ratio = recommendation.get("risk_reward_ratio", 0)
            if risk_reward_ratio < 1.0 and action != "HOLD":
                issues.append(f"Poor risk/reward ratio: {risk_reward_ratio:.2f}:1")
                quality_score -= 15

            # Validate confidence level
            confidence = recommendation.get("confidence", 0)
            if confidence < 0.3:
                issues.append(f"Low confidence level: {confidence:.1%}")
                quality_score -= 10
            elif confidence > 0.95:
                issues.append(f"Unrealistically high confidence: {confidence:.1%}")
                quality_score -= 5

            # Validate position size
            position_size = recommendation.get("position_size", 0)
            if position_size <= 0:
                issues.append("Position size must be positive")
                quality_score -= 10

            # Validate analysis consistency
            composite_score = analysis.get("composite_score", 0.5)
            if action in ["BUY", "STRONG_BUY"] and composite_score < 0.5:
                issues.append("Buy recommendation inconsistent with bearish composite score")
                quality_score -= 15
            elif action in ["SELL", "STRONG_SELL"] and composite_score > 0.5:
                issues.append("Sell recommendation inconsistent with bullish composite score")
                quality_score -= 15

            # Generate recommendations
            recommendations = self._generate_validation_recommendations(issues, quality_score)

            quality_score = max(0, quality_score)
            is_valid = len(issues) == 0 and quality_score >= 70

            self.completed_tasks += 1
            return {
                "component": "trading_recommendation",
                "symbol": symbol,
                "is_valid": is_valid,
                "quality_score": quality_score,
                "confidence": 0.95,
                "issues": issues,
                "recommendations": recommendations,
                "validation_details": {
                    "price_validation": "passed" if not any("price" in issue.lower() for issue in issues) else "failed",
                    "ratio_validation": "passed" if risk_reward_ratio >= 1.0 or action == "HOLD" else "failed",
                    "confidence_validation": "passed" if 0.3 <= confidence <= 0.95 else "failed",
                    "consistency_validation": "passed" if not any("inconsistent" in issue.lower() for issue in issues) else "failed"
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Trading recommendation validation failed: {e}")
            return {"error": str(e), "component": "trading_recommendation"}

    async def _validate_analysis_results(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate analysis results from the analysis agent"""
        try:
            analysis = data.get("analysis_result", {})
            symbol = data.get("symbol", "")

            issues = []
            quality_score = 100.0

            # Validate required fields
            required_fields = ["composite_score", "confidence", "component_scores"]
            for field in required_fields:
                if field not in analysis:
                    issues.append(f"Missing required field: {field}")
                    quality_score -= 20

            # Validate score ranges
            composite_score = analysis.get("composite_score", 0.5)
            if not (0.0 <= composite_score <= 1.0):
                issues.append(f"Composite score out of range: {composite_score}")
                quality_score -= 15

            confidence = analysis.get("confidence", 0.5)
            if not (0.0 <= confidence <= 1.0):
                issues.append(f"Confidence out of range: {confidence}")
                quality_score -= 15

            # Validate component scores
            component_scores = analysis.get("component_scores", {})
            for component, score in component_scores.items():
                if not (0.0 <= score <= 1.0):
                    issues.append(f"{component} score out of range: {score}")
                    quality_score -= 10

            # Validate consistency between composite and component scores
            if component_scores:
                expected_composite = sum(component_scores.values()) / len(component_scores)
                if abs(composite_score - expected_composite) > 0.2:
                    issues.append("Composite score inconsistent with component scores")
                    quality_score -= 15

            # Validate recommendation consistency
            recommendation = analysis.get("recommendation", {})
            if recommendation:
                rec_action = recommendation.get("action", "")
                if rec_action in ["BUY"] and composite_score < 0.5:
                    issues.append("Buy recommendation inconsistent with low composite score")
                    quality_score -= 10
                elif rec_action in ["SELL"] and composite_score > 0.5:
                    issues.append("Sell recommendation inconsistent with high composite score")
                    quality_score -= 10

            recommendations = self._generate_validation_recommendations(issues, quality_score)
            quality_score = max(0, quality_score)
            is_valid = len(issues) == 0 and quality_score >= 70

            self.completed_tasks += 1
            return {
                "component": "analysis_results",
                "symbol": symbol,
                "is_valid": is_valid,
                "quality_score": quality_score,
                "confidence": 0.92,
                "issues": issues,
                "recommendations": recommendations,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Analysis results validation failed: {e}")
            return {"error": str(e), "component": "analysis_results"}

    async def _validate_risk_assessment(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate risk assessment from the risk management agent"""
        try:
            risk_data = data.get("risk_assessment", {})
            symbol = data.get("symbol", "")

            issues = []
            quality_score = 100.0

            # Validate required fields
            required_fields = ["risk_score", "risk_level", "metrics"]
            for field in required_fields:
                if field not in risk_data:
                    issues.append(f"Missing required field: {field}")
                    quality_score -= 20

            # Validate risk score
            risk_score = risk_data.get("risk_score", 50)
            if not (0 <= risk_score <= 100):
                issues.append(f"Risk score out of range: {risk_score}")
                quality_score -= 15

            # Validate risk level consistency
            risk_level = risk_data.get("risk_level", "")
            expected_level = self._classify_risk_level(risk_score)
            if risk_level != expected_level:
                issues.append(f"Risk level '{risk_level}' inconsistent with score {risk_score}")
                quality_score -= 10

            # Validate metrics
            metrics = risk_data.get("metrics", {})
            if "volatility" in metrics:
                volatility = metrics["volatility"]
                if volatility < 0 or volatility > 2.0:  # Reasonable volatility range
                    issues.append(f"Volatility out of reasonable range: {volatility}")
                    quality_score -= 10

            if "var_1day" in metrics and "var_5day" in metrics:
                var_1day = metrics["var_1day"]
                var_5day = metrics["var_5day"]
                if var_5day < var_1day:  # 5-day VaR should be higher
                    issues.append("5-day VaR should be higher than 1-day VaR")
                    quality_score -= 10

            recommendations = self._generate_validation_recommendations(issues, quality_score)
            quality_score = max(0, quality_score)
            is_valid = len(issues) == 0 and quality_score >= 70

            self.completed_tasks += 1
            return {
                "component": "risk_assessment",
                "symbol": symbol,
                "is_valid": is_valid,
                "quality_score": quality_score,
                "confidence": 0.94,
                "issues": issues,
                "recommendations": recommendations,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Risk assessment validation failed: {e}")
            return {"error": str(e), "component": "risk_assessment"}

    async def _validate_pattern_detection(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate pattern detection from the pattern detection agent"""
        try:
            pattern_data = data.get("pattern_detection", {})
            symbol = data.get("symbol", "")

            issues = []
            quality_score = 100.0

            # Validate required fields
            required_fields = ["pattern_detected", "strength", "confidence"]
            for field in required_fields:
                if field not in pattern_data:
                    issues.append(f"Missing required field: {field}")
                    quality_score -= 20

            # Validate pattern strength
            strength = pattern_data.get("strength", 0.0)
            if not (0.0 <= strength <= 1.0):
                issues.append(f"Pattern strength out of range: {strength}")
                quality_score -= 15

            # Validate confidence
            confidence = pattern_data.get("confidence", 0.0)
            if not (0.0 <= confidence <= 1.0):
                issues.append(f"Confidence out of range: {confidence}")
                quality_score -= 15

            # Validate pattern detection consistency
            pattern_detected = pattern_data.get("pattern_detected", False)
            if pattern_detected and strength < 0.3:
                issues.append("Pattern detected but strength is very low")
                quality_score -= 10
            elif not pattern_detected and strength > 0.7:
                issues.append("Pattern not detected but strength is high")
                quality_score -= 10

            # Validate Lee Method criteria if available
            criteria_analysis = pattern_data.get("criteria_analysis", {})
            if criteria_analysis:
                criteria_met = criteria_analysis.get("criteria_met", 0)
                if pattern_detected and criteria_met < 3:
                    issues.append("Lee Method pattern detected but not all 3 criteria met")
                    quality_score -= 15

            recommendations = self._generate_validation_recommendations(issues, quality_score)
            quality_score = max(0, quality_score)
            is_valid = len(issues) == 0 and quality_score >= 70

            self.completed_tasks += 1
            return {
                "component": "pattern_detection",
                "symbol": symbol,
                "is_valid": is_valid,
                "quality_score": quality_score,
                "confidence": 0.90,
                "issues": issues,
                "recommendations": recommendations,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Pattern detection validation failed: {e}")
            return {"error": str(e), "component": "pattern_detection"}

    async def _cross_validate_agents(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Cross-validate results from multiple agents"""
        try:
            agent_results = data.get("agent_results", {})
            symbol = data.get("symbol", "")

            issues = []
            quality_score = 100.0

            # Check for consistency between agents
            if "analysis" in agent_results and "risk" in agent_results:
                analysis_score = agent_results["analysis"].get("composite_score", 0.5)
                risk_score = agent_results["risk"].get("risk_score", 50) / 100.0

                # High analysis score should correlate with lower risk
                if analysis_score > 0.7 and risk_score > 0.7:
                    issues.append("High analysis score inconsistent with high risk score")
                    quality_score -= 15

            # Check pattern detection consistency with analysis
            if "pattern" in agent_results and "analysis" in agent_results:
                pattern_detected = agent_results["pattern"].get("pattern_detected", False)
                analysis_score = agent_results["analysis"].get("composite_score", 0.5)

                if pattern_detected and analysis_score < 0.4:
                    issues.append("Pattern detected but analysis score is low")
                    quality_score -= 10

            # Check trading recommendation consistency
            if "trading" in agent_results:
                trading_rec = agent_results["trading"].get("recommendation", {})
                action = trading_rec.get("action", "")

                if "analysis" in agent_results:
                    analysis_score = agent_results["analysis"].get("composite_score", 0.5)
                    if action in ["BUY", "STRONG_BUY"] and analysis_score < 0.5:
                        issues.append("Buy recommendation inconsistent with bearish analysis")
                        quality_score -= 15
                    elif action in ["SELL", "STRONG_SELL"] and analysis_score > 0.5:
                        issues.append("Sell recommendation inconsistent with bullish analysis")
                        quality_score -= 15

            recommendations = self._generate_validation_recommendations(issues, quality_score)
            quality_score = max(0, quality_score)
            is_valid = len(issues) == 0 and quality_score >= 70

            self.completed_tasks += 1
            return {
                "component": "cross_validation",
                "symbol": symbol,
                "is_valid": is_valid,
                "quality_score": quality_score,
                "confidence": 0.88,
                "issues": issues,
                "recommendations": recommendations,
                "agents_validated": list(agent_results.keys()),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Cross-validation failed: {e}")
            return {"error": str(e), "component": "cross_validation"}

    async def _system_health_check(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform system health check"""
        try:
            system_status = data.get("system_status", {})

            issues = []
            quality_score = 100.0

            # Check agent availability
            registered_agents = system_status.get("registered_agents", 0)
            if registered_agents < 4:  # Minimum required agents
                issues.append(f"Insufficient agents registered: {registered_agents}")
                quality_score -= 20

            # Check active tasks
            active_tasks = system_status.get("active_tasks", 0)
            if active_tasks > 50:  # Too many active tasks
                issues.append(f"High number of active tasks: {active_tasks}")
                quality_score -= 10

            # Check agent performance
            agents = system_status.get("agents", {})
            for agent_role, agent_status in agents.items():
                performance = agent_status.get("performance", {})
                success_rate = performance.get("success_rate", 0)

                if success_rate < 0.8:  # 80% minimum success rate
                    issues.append(f"{agent_role} has low success rate: {success_rate:.1%}")
                    quality_score -= 15

            recommendations = self._generate_system_recommendations(issues, quality_score)
            quality_score = max(0, quality_score)
            is_valid = len(issues) == 0 and quality_score >= 80

            self.completed_tasks += 1
            return {
                "component": "system_health",
                "is_valid": is_valid,
                "quality_score": quality_score,
                "confidence": 0.96,
                "issues": issues,
                "recommendations": recommendations,
                "system_metrics": {
                    "registered_agents": registered_agents,
                    "active_tasks": active_tasks,
                    "overall_health": "healthy" if is_valid else "degraded"
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"System health check failed: {e}")
            return {"error": str(e), "component": "system_health"}

    def _setup_validation_rules(self) -> Dict[str, Any]:
        """Setup validation rules and thresholds"""
        return {
            "min_quality_score": 70,
            "min_confidence": 0.3,
            "max_confidence": 0.95,
            "min_risk_reward_ratio": 1.0,
            "score_ranges": {
                "composite_score": (0.0, 1.0),
                "sentiment_score": (0.0, 1.0),
                "technical_score": (0.0, 1.0),
                "risk_score": (0, 100),
                "pattern_strength": (0.0, 1.0)
            }
        }

    def _classify_risk_level(self, risk_score: float) -> str:
        """Classify risk level based on score"""
        if risk_score >= 80:
            return "very_high"
        elif risk_score >= 60:
            return "high"
        elif risk_score >= 40:
            return "medium"
        elif risk_score >= 20:
            return "low"
        else:
            return "very_low"

    def _generate_validation_recommendations(self, issues: List[str], quality_score: float) -> List[str]:
        """Generate validation recommendations"""
        recommendations = []

        if quality_score < 50:
            recommendations.append("Critical validation failures - review all components")
        elif quality_score < 70:
            recommendations.append("Multiple validation issues - address before proceeding")
        elif quality_score < 85:
            recommendations.append("Minor validation issues - consider improvements")

        if any("price" in issue.lower() for issue in issues):
            recommendations.append("Review price calculations and relationships")

        if any("inconsistent" in issue.lower() for issue in issues):
            recommendations.append("Check consistency between analysis components")

        if any("range" in issue.lower() for issue in issues):
            recommendations.append("Verify all scores and values are within valid ranges")

        return recommendations

    def _generate_system_recommendations(self, issues: List[str], quality_score: float) -> List[str]:
        """Generate system-level recommendations"""
        recommendations = []

        if quality_score < 60:
            recommendations.append("System health critical - immediate attention required")
        elif quality_score < 80:
            recommendations.append("System performance degraded - investigate issues")

        if any("agents" in issue.lower() for issue in issues):
            recommendations.append("Check agent registration and initialization")

        if any("tasks" in issue.lower() for issue in issues):
            recommendations.append("Monitor task queue and processing capacity")

        if any("success rate" in issue.lower() for issue in issues):
            recommendations.append("Review agent performance and error handling")

        return recommendations

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = ["AtlasValidationAgent", "ValidationResult"]