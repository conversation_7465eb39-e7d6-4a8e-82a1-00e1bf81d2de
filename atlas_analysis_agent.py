"""
A.T.L.A.S. Analysis Agent
Advanced sentiment analysis and technical analysis with Grok AI integration
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from atlas_multi_agent_core import (
    AtlasBaseAgent, AgentRole, AgentCapabilities, MultiAgentTask
)
from atlas_grok_integration import AtlasGrokIntegrationEngine
from atlas_enhanced_market_data import AtlasEnhancedMarketDataEngine

logger = logging.getLogger(__name__)

@dataclass
class AnalysisResult:
    """Result of comprehensive analysis"""
    symbol: str
    sentiment_score: float
    sentiment_label: str
    technical_score: float
    fundamental_score: float
    composite_score: float
    confidence: float
    analysis_details: Dict[str, Any]
    timestamp: datetime

class AtlasAnalysisAgent(AtlasBaseAgent):
    """Analysis agent for sentiment, technical, and fundamental analysis"""

    def __init__(self):
        super().__init__(AgentRole.ANALYSIS_ENGINE)
        self.grok_integration = None
        self.market_data_engine = None

    def _define_capabilities(self) -> AgentCapabilities:
        """Define analysis agent capabilities"""
        return AgentCapabilities(
            role=AgentRole.ANALYSIS_ENGINE,
            supported_tasks=[
                "sentiment_analysis",
                "technical_analysis",
                "fundamental_analysis",
                "composite_analysis",
                "market_context_analysis",
                "news_impact_analysis"
            ],
            max_concurrent_tasks=4,
            average_processing_time=35.0,
            success_rate=0.88,
            dependencies=[AgentRole.DATA_VALIDATOR]
        )

    async def _initialize_agent(self):
        """Initialize the analysis agent"""
        try:
            # Initialize Grok AI integration
            self.grok_integration = AtlasGrokIntegrationEngine()
            await self.grok_integration.initialize()

            # Initialize market data engine
            self.market_data_engine = AtlasEnhancedMarketDataEngine()
            await self.market_data_engine.initialize()

            self.logger.info("Analysis agent initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize analysis agent: {e}")
            raise

    async def execute_task(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Execute an analysis task"""
        try:
            task_type = task.input_data.get("task_type", "composite_analysis")
            symbol = task.input_data.get("symbol", "")

            if task_type == "sentiment_analysis":
                return await self._perform_sentiment_analysis(symbol)
            elif task_type == "technical_analysis":
                return await self._perform_technical_analysis(symbol)
            elif task_type == "fundamental_analysis":
                return await self._perform_fundamental_analysis(symbol)
            elif task_type == "composite_analysis":
                return await self._perform_composite_analysis(symbol)
            elif task_type == "market_context_analysis":
                return await self._analyze_market_context(symbol)
            elif task_type == "news_impact_analysis":
                return await self._analyze_news_impact(symbol)
            else:
                return {"error": f"Unknown task type: {task_type}"}

        except Exception as e:
            self.logger.error(f"Analysis task failed: {e}")
            self.failed_tasks += 1
            return {"error": str(e)}

    async def _perform_sentiment_analysis(self, symbol: str) -> Dict[str, Any]:
        """Perform sentiment analysis using Grok AI"""
        try:
            # Get market data for context
            market_data = await self.market_data_engine.get_real_time_quote(symbol)

            if not market_data:
                return {
                    "sentiment_score": 0.0,
                    "sentiment_label": "neutral",
                    "error": "No market data available",
                    "symbol": symbol
                }

            # Prepare context for Grok AI
            context = {
                "symbol": symbol,
                "current_price": market_data.get("price", 0),
                "price_change": market_data.get("change_percent", 0),
                "volume": market_data.get("volume", 0),
                "analysis_type": "sentiment"
            }

            # Get sentiment analysis from Grok AI
            grok_response = await self.grok_integration.analyze_market_sentiment(
                symbol=symbol,
                context=context
            )

            if not grok_response or "error" in grok_response:
                # Fallback sentiment analysis
                sentiment_score = self._calculate_fallback_sentiment(market_data)
                sentiment_label = self._classify_sentiment(sentiment_score)

                return {
                    "sentiment_score": sentiment_score,
                    "sentiment_label": sentiment_label,
                    "confidence": 0.6,
                    "source": "fallback",
                    "symbol": symbol,
                    "timestamp": datetime.now().isoformat()
                }

            # Extract sentiment from Grok response
            sentiment_score = grok_response.get("sentiment_score", 0.0)
            sentiment_label = self._classify_sentiment(sentiment_score)
            confidence = grok_response.get("confidence", 0.8)

            self.completed_tasks += 1
            return {
                "sentiment_score": sentiment_score,
                "sentiment_label": sentiment_label,
                "confidence": confidence,
                "source": "grok_ai",
                "analysis_details": grok_response.get("details", {}),
                "symbol": symbol,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Sentiment analysis failed for {symbol}: {e}")
            return {
                "sentiment_score": 0.0,
                "sentiment_label": "neutral",
                "error": str(e),
                "symbol": symbol
            }

    async def _perform_technical_analysis(self, symbol: str) -> Dict[str, Any]:
        """Perform technical analysis"""
        try:
            # Get comprehensive market data
            market_data = await self.market_data_engine.get_real_time_quote(symbol)

            if not market_data:
                return {"technical_score": 0.0, "error": "No market data available"}

            # Calculate technical indicators
            technical_indicators = await self._calculate_technical_indicators(symbol, market_data)

            # Calculate composite technical score
            technical_score = self._calculate_technical_score(technical_indicators)

            return {
                "technical_score": technical_score,
                "technical_rating": self._classify_technical_score(technical_score),
                "indicators": technical_indicators,
                "symbol": symbol,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Technical analysis failed for {symbol}: {e}")
            return {"technical_score": 0.0, "error": str(e)}

    async def _perform_fundamental_analysis(self, symbol: str) -> Dict[str, Any]:
        """Perform fundamental analysis"""
        try:
            # Get fundamental data (would integrate with financial APIs)
            fundamental_data = await self._get_fundamental_data(symbol)

            # Calculate fundamental score
            fundamental_score = self._calculate_fundamental_score(fundamental_data)

            return {
                "fundamental_score": fundamental_score,
                "fundamental_rating": self._classify_fundamental_score(fundamental_score),
                "metrics": fundamental_data,
                "symbol": symbol,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Fundamental analysis failed for {symbol}: {e}")
            return {"fundamental_score": 0.0, "error": str(e)}

    async def _perform_composite_analysis(self, symbol: str) -> Dict[str, Any]:
        """Perform comprehensive composite analysis"""
        try:
            # Get all analysis components
            sentiment_result = await self._perform_sentiment_analysis(symbol)
            technical_result = await self._perform_technical_analysis(symbol)
            fundamental_result = await self._perform_fundamental_analysis(symbol)

            # Extract scores
            sentiment_score = sentiment_result.get("sentiment_score", 0.0)
            technical_score = technical_result.get("technical_score", 0.0)
            fundamental_score = fundamental_result.get("fundamental_score", 0.0)

            # Calculate weighted composite score
            composite_score = (
                sentiment_score * 0.3 +
                technical_score * 0.4 +
                fundamental_score * 0.3
            )

            # Calculate overall confidence
            confidences = [
                sentiment_result.get("confidence", 0.5),
                0.8,  # Technical analysis confidence
                0.7   # Fundamental analysis confidence
            ]
            overall_confidence = sum(confidences) / len(confidences)

            # Generate recommendation
            recommendation = self._generate_recommendation(
                composite_score, sentiment_score, technical_score, fundamental_score
            )

            self.completed_tasks += 1
            return {
                "composite_score": composite_score,
                "composite_rating": self._classify_composite_score(composite_score),
                "confidence": overall_confidence,
                "recommendation": recommendation,
                "component_scores": {
                    "sentiment": sentiment_score,
                    "technical": technical_score,
                    "fundamental": fundamental_score
                },
                "detailed_analysis": {
                    "sentiment": sentiment_result,
                    "technical": technical_result,
                    "fundamental": fundamental_result
                },
                "symbol": symbol,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Composite analysis failed for {symbol}: {e}")
            return {"composite_score": 0.0, "error": str(e)}

    async def _analyze_market_context(self, symbol: str) -> Dict[str, Any]:
        """Analyze broader market context"""
        try:
            # Get market context from Grok AI
            context_analysis = await self.grok_integration.get_market_context(symbol)

            if not context_analysis:
                return {"market_context": "neutral", "error": "No context data available"}

            return {
                "market_context": context_analysis.get("context", "neutral"),
                "market_sentiment": context_analysis.get("overall_sentiment", "neutral"),
                "sector_performance": context_analysis.get("sector_performance", {}),
                "key_factors": context_analysis.get("key_factors", []),
                "symbol": symbol,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {"market_context": "neutral", "error": str(e)}

    async def _analyze_news_impact(self, symbol: str) -> Dict[str, Any]:
        """Analyze news impact on stock"""
        try:
            # Get news analysis from Grok AI
            news_analysis = await self.grok_integration.analyze_news_impact(symbol)

            if not news_analysis:
                return {"news_impact": "neutral", "error": "No news data available"}

            return {
                "news_impact": news_analysis.get("impact", "neutral"),
                "impact_score": news_analysis.get("impact_score", 0.0),
                "key_headlines": news_analysis.get("headlines", []),
                "sentiment_shift": news_analysis.get("sentiment_shift", 0.0),
                "symbol": symbol,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {"news_impact": "neutral", "error": str(e)}

    def _calculate_fallback_sentiment(self, market_data: Dict[str, Any]) -> float:
        """Calculate fallback sentiment based on price action"""
        price_change = market_data.get("change_percent", 0.0)
        volume_ratio = market_data.get("volume", 0) / market_data.get("avg_volume", 1)

        # Simple sentiment calculation
        sentiment = 0.5  # Neutral baseline

        # Price change impact
        if price_change > 0:
            sentiment += min(price_change / 10.0, 0.3)  # Cap at 0.3
        else:
            sentiment += max(price_change / 10.0, -0.3)  # Floor at -0.3

        # Volume confirmation
        if volume_ratio > 1.5:  # High volume
            sentiment += 0.1 if price_change > 0 else -0.1

        return max(0.0, min(1.0, sentiment))  # Clamp between 0 and 1

    def _classify_sentiment(self, score: float) -> str:
        """Classify sentiment score into label"""
        if score >= 0.7:
            return "very_bullish"
        elif score >= 0.6:
            return "bullish"
        elif score >= 0.4:
            return "neutral"
        elif score >= 0.3:
            return "bearish"
        else:
            return "very_bearish"

    async def _calculate_technical_indicators(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate technical indicators"""
        # This would normally calculate RSI, MACD, Moving Averages, etc.
        # For now, using simplified calculations based on available data

        price = market_data.get("price", 0)
        high = market_data.get("high", price)
        low = market_data.get("low", price)
        volume = market_data.get("volume", 0)

        return {
            "price_position": (price - low) / max(high - low, 0.01),  # Where price is in daily range
            "volume_strength": min(volume / 1000000, 2.0),  # Volume strength indicator
            "momentum": market_data.get("change_percent", 0) / 100.0,
            "volatility": (high - low) / price if price > 0 else 0
        }

    def _calculate_technical_score(self, indicators: Dict[str, Any]) -> float:
        """Calculate composite technical score"""
        price_position = indicators.get("price_position", 0.5)
        volume_strength = min(indicators.get("volume_strength", 1.0), 1.0)
        momentum = indicators.get("momentum", 0.0)

        # Weighted technical score
        score = (
            price_position * 0.3 +
            volume_strength * 0.2 +
            (0.5 + momentum) * 0.5  # Normalize momentum around 0.5
        )

        return max(0.0, min(1.0, score))

    def _classify_technical_score(self, score: float) -> str:
        """Classify technical score"""
        if score >= 0.8:
            return "very_strong"
        elif score >= 0.6:
            return "strong"
        elif score >= 0.4:
            return "neutral"
        elif score >= 0.2:
            return "weak"
        else:
            return "very_weak"

    async def _get_fundamental_data(self, symbol: str) -> Dict[str, Any]:
        """Get fundamental data (placeholder - would integrate with financial APIs)"""
        # This would normally fetch P/E ratio, EPS, revenue growth, etc.
        return {
            "pe_ratio": 15.0,  # Placeholder
            "eps_growth": 0.1,  # Placeholder
            "revenue_growth": 0.08,  # Placeholder
            "debt_to_equity": 0.3  # Placeholder
        }

    def _calculate_fundamental_score(self, data: Dict[str, Any]) -> float:
        """Calculate fundamental score"""
        # Simplified fundamental scoring
        pe_score = 1.0 - min(data.get("pe_ratio", 20) / 30.0, 1.0)  # Lower P/E is better
        growth_score = min(data.get("eps_growth", 0) * 5, 1.0)  # Higher growth is better

        return (pe_score + growth_score) / 2.0

    def _classify_fundamental_score(self, score: float) -> str:
        """Classify fundamental score"""
        if score >= 0.8:
            return "excellent"
        elif score >= 0.6:
            return "good"
        elif score >= 0.4:
            return "fair"
        elif score >= 0.2:
            return "poor"
        else:
            return "very_poor"

    def _classify_composite_score(self, score: float) -> str:
        """Classify composite score"""
        if score >= 0.8:
            return "strong_buy"
        elif score >= 0.6:
            return "buy"
        elif score >= 0.4:
            return "hold"
        elif score >= 0.2:
            return "sell"
        else:
            return "strong_sell"

    def _generate_recommendation(self, composite: float, sentiment: float, technical: float, fundamental: float) -> Dict[str, Any]:
        """Generate trading recommendation"""
        # Determine primary recommendation
        if composite >= 0.7:
            action = "BUY"
            confidence = "HIGH"
        elif composite >= 0.6:
            action = "BUY"
            confidence = "MEDIUM"
        elif composite >= 0.4:
            action = "HOLD"
            confidence = "MEDIUM"
        elif composite >= 0.3:
            action = "SELL"
            confidence = "MEDIUM"
        else:
            action = "SELL"
            confidence = "HIGH"

        # Generate supporting reasons
        reasons = []
        if sentiment >= 0.6:
            reasons.append("Positive market sentiment")
        elif sentiment <= 0.4:
            reasons.append("Negative market sentiment")

        if technical >= 0.6:
            reasons.append("Strong technical indicators")
        elif technical <= 0.4:
            reasons.append("Weak technical indicators")

        if fundamental >= 0.6:
            reasons.append("Solid fundamentals")
        elif fundamental <= 0.4:
            reasons.append("Weak fundamentals")

        return {
            "action": action,
            "confidence": confidence,
            "reasons": reasons,
            "risk_level": "HIGH" if composite < 0.3 or composite > 0.8 else "MEDIUM"
        }

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = ["AtlasAnalysisAgent", "AnalysisResult"]