"""
A.T.L.A.S. Market Core - Real Market Data Implementation
Provides real market data using FMP API and other live sources
"""

import logging
import aiohttp
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class MarketStatus(Enum):
    """Market status enumeration"""
    OPEN = "open"
    CLOSED = "closed"
    PRE_MARKET = "pre_market"
    AFTER_HOURS = "after_hours"
    UNKNOWN = "unknown"

@dataclass
class Quote:
    """Market quote data"""
    symbol: str
    price: float
    volume: int
    change: float
    change_percent: float
    timestamp: datetime
    bid: Optional[float] = None
    ask: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None

@dataclass
class MarketHours:
    """Market hours information"""
    is_open: bool
    next_open: Optional[datetime]
    next_close: Optional[datetime]

class MarketDataProvider:
    """Real market data provider using FMP API"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.fmp_config = None
        self.session = None
        self.logger.info("[REAL] Real market data provider initialized")
    
    async def initialize(self):
        """Initialize the market data provider"""
        try:
            from config import get_api_config
            self.fmp_config = get_api_config('fmp')
            
            if not self.fmp_config or not self.fmp_config.get('available'):
                self.logger.error("FMP API not configured")
                return False
                
            self.session = aiohttp.ClientSession()
            self.logger.info("✅ Real market data provider ready")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize market data provider: {e}")
            return False
    
    async def get_quote(self, symbol: str) -> Optional[Quote]:
        """Get real quote for symbol using FMP API"""
        try:
            if not self.fmp_config or not self.session:
                return None
                
            url = f"https://financialmodelingprep.com/api/v3/quote/{symbol}"
            params = {'apikey': self.fmp_config['api_key']}
            
            async with self.session.get(url, params=params, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    if data and len(data) > 0:
                        quote_data = data[0]
                        return Quote(
                            symbol=quote_data.get('symbol', symbol),
                            price=quote_data.get('price', 0.0),
                            volume=quote_data.get('volume', 0),
                            change=quote_data.get('change', 0.0),
                            change_percent=quote_data.get('changesPercentage', 0.0),
                            timestamp=datetime.now(),
                            bid=quote_data.get('bid'),
                            ask=quote_data.get('ask'),
                            high=quote_data.get('dayHigh'),
                            low=quote_data.get('dayLow')
                        )
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get quote for {symbol}: {e}")
            return None
    
    async def get_quotes(self, symbols: List[str]) -> Dict[str, Quote]:
        """Get quotes for multiple symbols"""
        quotes = {}
        for symbol in symbols:
            quote = await self.get_quote(symbol)
            if quote:
                quotes[symbol] = quote
        return quotes
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.session:
            await self.session.close()

class AtlasMarketEngine:
    """Real market engine implementation"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.data_provider = MarketDataProvider()
        self.status = MarketStatus.UNKNOWN
        self.logger.info("[REAL] Real market engine initialized")
    
    async def initialize(self):
        """Initialize the market engine"""
        try:
            success = await self.data_provider.initialize()
            if success:
                self.logger.info("✅ Real market engine ready")
                return True
            else:
                self.logger.error("❌ Market engine initialization failed")
                return False
        except Exception as e:
            self.logger.error(f"Market engine initialization error: {e}")
            return False
    
    async def get_quote(self, symbol: str) -> Optional[Quote]:
        """Get current quote for a symbol"""
        return await self.data_provider.get_quote(symbol)
    
    async def get_quotes(self, symbols: List[str]) -> Dict[str, Quote]:
        """Get quotes for multiple symbols"""
        return await self.data_provider.get_quotes(symbols)
    
    async def get_market_hours(self) -> MarketHours:
        """Get market hours information"""
        # For now, return basic market hours (can be enhanced with real API)
        now = datetime.now()
        market_open_hour = 9  # 9:30 AM EST
        market_close_hour = 16  # 4:00 PM EST
        
        current_hour = now.hour
        is_open = market_open_hour <= current_hour < market_close_hour
        
        return MarketHours(
            is_open=is_open,
            next_open=None,  # Can be calculated based on market calendar
            next_close=None
        )
    
    async def start_real_time_data(self, symbols: List[str]):
        """Start real-time data feed"""
        self.logger.info(f"[REAL] Real-time data started for {len(symbols)} symbols")
    
    async def stop_real_time_data(self):
        """Stop real-time data feed"""
        self.logger.info("[REAL] Real-time data stopped")
    
    def get_engine_status(self) -> Dict[str, Any]:
        """Get engine status"""
        return {
            "status": "active",
            "mode": "real_data",
            "data_provider": "fmp",
            "real_time_active": True
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        await self.data_provider.cleanup()

# Export the main classes for compatibility
__all__ = [
    'AtlasMarketEngine',
    'MarketDataProvider', 
    'MarketStatus',
    'Quote',
    'MarketHours'
]

# Create global instances for backward compatibility
market_engine = AtlasMarketEngine()
market_data_provider = MarketDataProvider()
