<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A.T.L.A.S. v5.0 - Advanced Trading & Learning Analytics System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #ffffff;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem 2rem;
            border-bottom: 2px solid #4CAF50;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-title {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: #4CAF50;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .input-group {
            margin-bottom: 1rem;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .input-group input, .input-group select, .input-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            font-size: 1rem;
        }
        
        .input-group input::placeholder, .input-group textarea::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        .results {
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
        }
        
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .signal-item {
            background: rgba(76, 175, 80, 0.2);
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        
        .quote-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .quote-item {
            text-align: center;
            padding: 0.75rem;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
        }
        
        .quote-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .quote-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 0.25rem;
        }
        
        .positive { color: #4CAF50; }
        .negative { color: #f44336; }
        
        .chat-container {
            height: 400px;
            display: flex;
            flex-direction: column;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 8px;
        }
        
        .message.user {
            background: rgba(76, 175, 80, 0.3);
            margin-left: 2rem;
        }
        
        .message.ai {
            background: rgba(33, 150, 243, 0.3);
            margin-right: 2rem;
        }
        
        .chat-input {
            display: flex;
            gap: 0.5rem;
        }
        
        .chat-input input {
            flex: 1;
        }
        
        .chat-input button {
            width: auto;
            margin: 0;
            padding: 0.75rem 1rem;
        }

        /* Terminal Styles */
        .terminal {
            background: #1a1a1a;
            border-radius: 8px;
            overflow: hidden;
            font-family: 'Courier New', monospace;
            height: 400px;
            display: flex;
            flex-direction: column;
        }

        .terminal-header {
            background: #333;
            color: #fff;
            padding: 8px 16px;
            font-size: 0.9rem;
            border-bottom: 1px solid #555;
        }

        .terminal-output {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            background: #000;
            color: #00ff00;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .terminal-line {
            margin-bottom: 4px;
            word-wrap: break-word;
        }

        .terminal-line.system {
            color: #4CAF50;
        }

        .terminal-line.error {
            color: #ff4444;
        }

        .terminal-line.warning {
            color: #ffaa00;
        }

        .terminal-line.info {
            color: #00aaff;
        }

        .terminal-input {
            background: #222;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            border-top: 1px solid #555;
        }

        .terminal-prompt {
            color: #4CAF50;
            margin-right: 8px;
            font-weight: bold;
        }

        .terminal-input input {
            flex: 1;
            background: transparent;
            border: none;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            outline: none;
        }

        .terminal-input input:disabled {
            opacity: 0.5;
        }

        .ws-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .ws-status.connected {
            background: #4CAF50;
            color: white;
        }

        .ws-status.disconnected {
            background: #f44336;
            color: white;
        }

        .ws-status.connecting {
            background: #ff9800;
            color: white;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 0.8rem;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .btn-small:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🎯 A.T.L.A.S. v5.0</div>
        <div class="status-indicator">
            <div class="status-dot"></div>
            <span>System Online</span>
        </div>
    </div>

    <div class="container">
        <h1 style="text-align: center; margin-bottom: 1rem;">Advanced Trading & Learning Analytics System</h1>
        <p style="text-align: center; opacity: 0.8; margin-bottom: 2rem;">Real-time market analysis powered by Grok AI and Google Search</p>

        <div class="dashboard-grid">
            <!-- Market Quote Card -->
            <div class="card">
                <div class="card-title">📈 Market Quote</div>
                <div class="input-group">
                    <label for="quoteSymbol">Stock Symbol</label>
                    <input type="text" id="quoteSymbol" placeholder="Enter symbol (e.g., AAPL)" value="AAPL">
                </div>
                <button class="btn" onclick="getQuote()">Get Quote</button>
                <div id="quoteResults" class="results" style="display: none;"></div>
            </div>

            <!-- AI Chat Card -->
            <div class="card">
                <div class="card-title">🤖 AI Analysis</div>
                <div class="chat-container">
                    <div id="chatMessages" class="chat-messages">
                        <div class="message ai">
                            <strong>A.T.L.A.S. AI:</strong> Hello! I'm your AI trading assistant powered by Grok. Ask me about any stock analysis, market trends, or trading strategies.
                        </div>
                    </div>
                    <div class="chat-input">
                        <input type="text" id="chatInput" placeholder="Ask me about stocks, market analysis, or trading strategies..." onkeypress="handleChatKeyPress(event)">
                        <button onclick="sendChatMessage()">Send</button>
                    </div>
                </div>
            </div>

            <!-- Lee Method Signals Card -->
            <div class="card">
                <div class="card-title">🔍 Lee Method Signals</div>
                <p style="opacity: 0.8; margin-bottom: 1rem;">Real-time pattern detection and trading signals</p>
                <button class="btn" onclick="getLeeMethodSignals()">Scan for Signals</button>
                <div id="signalsResults" class="results" style="display: none;"></div>
            </div>

            <!-- Trading Positions Card -->
            <div class="card">
                <div class="card-title">💼 Trading Positions</div>
                <p style="opacity: 0.8; margin-bottom: 1rem;">Your current paper trading positions</p>
                <button class="btn" onclick="getTradingPositions()">View Positions</button>
                <div id="positionsResults" class="results" style="display: none;"></div>
            </div>

            <!-- System Health Card -->
            <div class="card">
                <div class="card-title">⚡ System Health</div>
                <button class="btn" onclick="getSystemHealth()">Check System Status</button>
                <div id="healthResults" class="results" style="display: none;"></div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card">
                <div class="card-title">⚡ Quick Actions</div>
                <div class="input-group">
                    <label for="analysisSymbol">Symbol for Analysis</label>
                    <input type="text" id="analysisSymbol" placeholder="Enter symbol" value="TSLA">
                </div>
                <button class="btn" onclick="quickAnalysis()" style="margin-bottom: 0.5rem;">Quick Analysis</button>
                <button class="btn" onclick="getNews()" style="margin-bottom: 0.5rem;">Get Latest News</button>
                <button class="btn" onclick="getSentiment()">Sentiment Analysis</button>
                <div id="quickResults" class="results" style="display: none;"></div>
            </div>

            <!-- Real-time Terminal Console -->
            <div class="card" style="grid-column: span 2;">
                <div class="card-title">
                    💻 Real-time Terminal Console
                    <div style="float: right; display: flex; gap: 10px; align-items: center;">
                        <span id="wsStatus" class="ws-status disconnected">Disconnected</span>
                        <button class="btn-small" onclick="toggleTerminal()" id="terminalToggle">Connect</button>
                        <button class="btn-small" onclick="clearTerminal()">Clear</button>
                    </div>
                </div>
                <div id="terminal" class="terminal">
                    <div class="terminal-header">
                        <span>A.T.L.A.S. System Terminal - Real-time Output</span>
                    </div>
                    <div id="terminalOutput" class="terminal-output">
                        <div class="terminal-line system">System ready. Click 'Connect' to start real-time monitoring...</div>
                    </div>
                    <div class="terminal-input">
                        <span class="terminal-prompt">atlas@v5.0:~$ </span>
                        <input type="text" id="terminalInput" placeholder="Enter command..." onkeypress="handleTerminalKeyPress(event)" disabled>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API Base URL
        const API_BASE = '';

        // Utility function to show loading
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = '<div class="loading"><div class="spinner"></div>Loading...</div>';
        }

        // Utility function to show results
        function showResults(elementId, content) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = content;
        }

        // Get Market Quote
        async function getQuote() {
            const symbol = document.getElementById('quoteSymbol').value.toUpperCase();
            if (!symbol) {
                alert('Please enter a stock symbol');
                return;
            }

            showLoading('quoteResults');

            try {
                const response = await fetch(`${API_BASE}/api/v1/quote/${symbol}`);
                const data = await response.json();

                if (response.ok) {
                    const changeClass = data.change >= 0 ? 'positive' : 'negative';
                    const changeSymbol = data.change >= 0 ? '+' : '';

                    const content = `
                        <div class="quote-display">
                            <div class="quote-item">
                                <div class="quote-value">$${data.price.toFixed(2)}</div>
                                <div class="quote-label">Price</div>
                            </div>
                            <div class="quote-item">
                                <div class="quote-value ${changeClass}">${changeSymbol}${data.change.toFixed(2)}</div>
                                <div class="quote-label">Change</div>
                            </div>
                            <div class="quote-item">
                                <div class="quote-value ${changeClass}">${changeSymbol}${data.changesPercentage.toFixed(2)}%</div>
                                <div class="quote-label">Change %</div>
                            </div>
                            <div class="quote-item">
                                <div class="quote-value">${data.volume.toLocaleString()}</div>
                                <div class="quote-label">Volume</div>
                            </div>
                        </div>
                        <p style="margin-top: 1rem; opacity: 0.8; text-align: center;">
                            Source: ${data.source} | Updated: ${new Date(data.timestamp).toLocaleTimeString()}
                        </p>
                    `;
                    showResults('quoteResults', content);
                } else {
                    showResults('quoteResults', `<p style="color: #f44336;">Error: ${data.error}</p>`);
                }
            } catch (error) {
                showResults('quoteResults', `<p style="color: #f44336;">Network error: ${error.message}</p>`);
            }
        }

        // Send Chat Message
        async function sendChatMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (!message) return;

            // Add user message to chat
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML += `
                <div class="message user">
                    <strong>You:</strong> ${message}
                </div>
            `;

            // Clear input
            input.value = '';

            // Add loading message
            chatMessages.innerHTML += `
                <div class="message ai" id="aiLoading">
                    <strong>A.T.L.A.S. AI:</strong> <div class="spinner" style="width: 20px; height: 20px; display: inline-block; margin-left: 10px;"></div> Analyzing...
                </div>
            `;
            chatMessages.scrollTop = chatMessages.scrollHeight;

            try {
                const response = await fetch(`${API_BASE}/api/v1/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();

                // Remove loading message
                document.getElementById('aiLoading').remove();

                if (response.ok) {
                    chatMessages.innerHTML += `
                        <div class="message ai">
                            <strong>A.T.L.A.S. AI:</strong> ${data.response || data.message || 'Analysis complete.'}
                        </div>
                    `;
                } else {
                    chatMessages.innerHTML += `
                        <div class="message ai">
                            <strong>A.T.L.A.S. AI:</strong> <span style="color: #f44336;">Error: ${data.error}</span>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('aiLoading').remove();
                chatMessages.innerHTML += `
                    <div class="message ai">
                        <strong>A.T.L.A.S. AI:</strong> <span style="color: #f44336;">Network error: ${error.message}</span>
                    </div>
                `;
            }

            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Handle Enter key in chat
        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendChatMessage();
            }
        }

        // Get Lee Method Signals
        async function getLeeMethodSignals() {
            showLoading('signalsResults');

            try {
                const response = await fetch(`${API_BASE}/api/v1/lee_method/signals`);
                const data = await response.json();

                if (response.ok) {
                    if (data.signals && data.signals.length > 0) {
                        let content = `<h4>Found ${data.total_signals} signals:</h4>`;
                        data.signals.forEach(signal => {
                            content += `
                                <div class="signal-item">
                                    <strong>${signal.symbol}</strong> - ${signal.signal_type}<br>
                                    <small>Strength: ${signal.strength} | Price: $${signal.price}</small>
                                </div>
                            `;
                        });
                        showResults('signalsResults', content);
                    } else {
                        showResults('signalsResults', `
                            <p>No signals found at this time.</p>
                            <p style="opacity: 0.8; margin-top: 0.5rem;">Scanner Status: ${data.status}</p>
                            <p style="opacity: 0.8;">Last Updated: ${new Date(data.timestamp).toLocaleTimeString()}</p>
                        `);
                    }
                } else {
                    showResults('signalsResults', `<p style="color: #f44336;">Error: ${data.error}</p>`);
                }
            } catch (error) {
                showResults('signalsResults', `<p style="color: #f44336;">Network error: ${error.message}</p>`);
            }
        }

        // Get Trading Positions
        async function getTradingPositions() {
            showLoading('positionsResults');

            try {
                const response = await fetch(`${API_BASE}/api/v1/trading/positions`);
                const data = await response.json();

                if (response.ok) {
                    if (data.positions && data.positions.length > 0) {
                        let content = '<h4>Current Positions:</h4>';
                        data.positions.forEach(position => {
                            const pnlClass = position.unrealized_pl >= 0 ? 'positive' : 'negative';
                            content += `
                                <div class="signal-item">
                                    <strong>${position.symbol}</strong> - ${position.qty} shares<br>
                                    <small>Avg Cost: $${position.avg_entry_price} | Current: $${position.current_price}</small><br>
                                    <small class="${pnlClass}">P&L: $${position.unrealized_pl} (${position.unrealized_plpc}%)</small>
                                </div>
                            `;
                        });
                        showResults('positionsResults', content);
                    } else {
                        showResults('positionsResults', '<p>No open positions found.</p>');
                    }
                } else {
                    showResults('positionsResults', `<p style="color: #f44336;">Error: ${data.error}</p>`);
                }
            } catch (error) {
                showResults('positionsResults', `<p style="color: #f44336;">Network error: ${error.message}</p>`);
            }
        }

        // Get System Health
        async function getSystemHealth() {
            showLoading('healthResults');

            try {
                const response = await fetch(`${API_BASE}/api/v1/health`);
                const data = await response.json();

                if (response.ok) {
                    let content = `
                        <h4>System Status: <span class="positive">${data.status.toUpperCase()}</span></h4>
                        <p><strong>Version:</strong> ${data.version}</p>
                        <p><strong>Uptime:</strong> ${Math.floor(data.uptime_seconds / 3600)}h ${Math.floor((data.uptime_seconds % 3600) / 60)}m</p>
                        <p><strong>API Keys:</strong> ${data.api_keys_configured ? '✅ Configured' : '❌ Missing'}</p>
                        <h5 style="margin-top: 1rem;">Components:</h5>
                    `;

                    Object.entries(data.components).forEach(([component, status]) => {
                        const statusIcon = status === 'active' ? '✅' : '❌';
                        content += `<p>${statusIcon} ${component.replace('_', ' ').toUpperCase()}: ${status}</p>`;
                    });

                    showResults('healthResults', content);
                } else {
                    showResults('healthResults', `<p style="color: #f44336;">Error: ${data.error}</p>`);
                }
            } catch (error) {
                showResults('healthResults', `<p style="color: #f44336;">Network error: ${error.message}</p>`);
            }
        }

        // Quick Analysis
        async function quickAnalysis() {
            const symbol = document.getElementById('analysisSymbol').value.toUpperCase();
            if (!symbol) {
                alert('Please enter a symbol');
                return;
            }

            showLoading('quickResults');

            try {
                const response = await fetch(`${API_BASE}/api/v1/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: `Provide a comprehensive analysis of ${symbol} including current price, technical indicators, and trading recommendation.`
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showResults('quickResults', `
                        <h4>Analysis for ${symbol}:</h4>
                        <div style="white-space: pre-wrap;">${data.response || data.message || 'Analysis complete.'}</div>
                    `);
                } else {
                    showResults('quickResults', `<p style="color: #f44336;">Error: ${data.error}</p>`);
                }
            } catch (error) {
                showResults('quickResults', `<p style="color: #f44336;">Network error: ${error.message}</p>`);
            }
        }

        // Get News
        async function getNews() {
            const symbol = document.getElementById('analysisSymbol').value.toUpperCase();
            if (!symbol) {
                alert('Please enter a symbol');
                return;
            }

            showLoading('quickResults');

            try {
                const response = await fetch(`${API_BASE}/api/v1/news/${symbol}`);
                const data = await response.json();

                if (response.ok && data.articles) {
                    let content = `<h4>Latest News for ${symbol}:</h4>`;
                    data.articles.slice(0, 5).forEach(article => {
                        content += `
                            <div style="margin-bottom: 1rem; padding: 0.5rem; background: rgba(0,0,0,0.2); border-radius: 5px;">
                                <strong>${article.title}</strong><br>
                                <small style="opacity: 0.8;">${article.source} - ${new Date(article.publishedAt).toLocaleDateString()}</small><br>
                                <p style="margin-top: 0.5rem;">${article.summary || article.description || 'No summary available.'}</p>
                            </div>
                        `;
                    });
                    showResults('quickResults', content);
                } else {
                    showResults('quickResults', `<p>No news available for ${symbol} at this time.</p>`);
                }
            } catch (error) {
                showResults('quickResults', `<p style="color: #f44336;">Network error: ${error.message}</p>`);
            }
        }

        // Get Sentiment
        async function getSentiment() {
            const symbol = document.getElementById('analysisSymbol').value.toUpperCase();
            if (!symbol) {
                alert('Please enter a symbol');
                return;
            }

            showLoading('quickResults');

            try {
                const response = await fetch(`${API_BASE}/api/v1/sentiment/${symbol}`);
                const data = await response.json();

                if (response.ok) {
                    const sentimentColor = data.overall_sentiment === 'positive' ? '#4CAF50' :
                                         data.overall_sentiment === 'negative' ? '#f44336' : '#FFC107';

                    showResults('quickResults', `
                        <h4>Sentiment Analysis for ${symbol}:</h4>
                        <div style="text-align: center; margin: 1rem 0;">
                            <div style="font-size: 2rem; color: ${sentimentColor};">
                                ${data.overall_sentiment ? data.overall_sentiment.toUpperCase() : 'NEUTRAL'}
                            </div>
                            <div style="opacity: 0.8;">
                                Score: ${data.sentiment_score || 'N/A'} | Confidence: ${data.confidence || 'N/A'}%
                            </div>
                        </div>
                        <p style="margin-top: 1rem;">${data.summary || 'Sentiment analysis complete.'}</p>
                    `);
                } else {
                    showResults('quickResults', `<p>Sentiment data not available for ${symbol} at this time.</p>`);
                }
            } catch (error) {
                showResults('quickResults', `<p style="color: #f44336;">Network error: ${error.message}</p>`);
            }
        }

        // Auto-refresh system status every 30 seconds
        setInterval(() => {
            const healthResults = document.getElementById('healthResults');
            if (healthResults.style.display === 'block') {
                getSystemHealth();
            }
        }, 30000);

        // WebSocket Terminal Functionality
        let terminalSocket = null;
        let isTerminalConnected = false;

        function addTerminalLine(message, type = 'system') {
            const output = document.getElementById('terminalOutput');
            const line = document.createElement('div');
            line.className = `terminal-line ${type}`;

            // Add timestamp
            const timestamp = new Date().toLocaleTimeString();
            line.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;

            output.appendChild(line);
            output.scrollTop = output.scrollHeight;
        }

        function updateWSStatus(status) {
            const statusElement = document.getElementById('wsStatus');
            const toggleButton = document.getElementById('terminalToggle');

            statusElement.className = `ws-status ${status}`;

            switch(status) {
                case 'connected':
                    statusElement.textContent = 'Connected';
                    toggleButton.textContent = 'Disconnect';
                    document.getElementById('terminalInput').disabled = false;
                    break;
                case 'connecting':
                    statusElement.textContent = 'Connecting...';
                    toggleButton.textContent = 'Cancel';
                    break;
                case 'disconnected':
                    statusElement.textContent = 'Disconnected';
                    toggleButton.textContent = 'Connect';
                    document.getElementById('terminalInput').disabled = true;
                    break;
            }
        }

        function connectTerminal() {
            if (terminalSocket) {
                return;
            }

            updateWSStatus('connecting');
            addTerminalLine('Connecting to A.T.L.A.S. real-time data stream...', 'info');

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/scanner`;

            terminalSocket = new WebSocket(wsUrl);

            terminalSocket.onopen = function(event) {
                isTerminalConnected = true;
                updateWSStatus('connected');
                addTerminalLine('✅ Connected to A.T.L.A.S. real-time terminal', 'system');
                addTerminalLine('Real-time scanner alerts, system logs, and command responses will appear here.', 'info');
            };

            terminalSocket.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);

                    if (data.type === 'scanner_alert') {
                        addTerminalLine(`🔍 SCANNER ALERT: ${data.symbol} - ${data.signal} (Strength: ${data.strength})`, 'warning');
                    } else if (data.type === 'system_log') {
                        addTerminalLine(`📊 SYSTEM: ${data.message}`, 'system');
                    } else if (data.type === 'command_response') {
                        addTerminalLine(`💻 RESPONSE: ${data.output}`, 'info');
                    } else if (data.type === 'error') {
                        addTerminalLine(`❌ ERROR: ${data.message}`, 'error');
                    } else {
                        // Generic message display
                        addTerminalLine(`📡 ${JSON.stringify(data)}`, 'info');
                    }
                } catch (e) {
                    addTerminalLine(`📡 RAW: ${event.data}`, 'info');
                }
            };

            terminalSocket.onclose = function(event) {
                isTerminalConnected = false;
                updateWSStatus('disconnected');
                addTerminalLine('❌ Connection closed. Click Connect to reconnect.', 'error');
                terminalSocket = null;
            };

            terminalSocket.onerror = function(error) {
                addTerminalLine(`❌ WebSocket error: ${error}`, 'error');
                updateWSStatus('disconnected');
                terminalSocket = null;
            };
        }

        function disconnectTerminal() {
            if (terminalSocket) {
                terminalSocket.close();
                terminalSocket = null;
            }
            isTerminalConnected = false;
            updateWSStatus('disconnected');
            addTerminalLine('🔌 Disconnected from real-time stream', 'warning');
        }

        function toggleTerminal() {
            if (isTerminalConnected) {
                disconnectTerminal();
            } else {
                connectTerminal();
            }
        }

        function clearTerminal() {
            const output = document.getElementById('terminalOutput');
            output.innerHTML = '<div class="terminal-line system">Terminal cleared.</div>';
        }

        function handleTerminalKeyPress(event) {
            if (event.key === 'Enter') {
                const input = document.getElementById('terminalInput');
                const command = input.value.trim();

                if (command) {
                    addTerminalLine(`$ ${command}`, 'info');

                    // Send command through WebSocket if connected
                    if (terminalSocket && isTerminalConnected) {
                        terminalSocket.send(JSON.stringify({
                            type: 'command',
                            command: command
                        }));
                    } else {
                        addTerminalLine('❌ Not connected to terminal. Click Connect first.', 'error');
                    }

                    input.value = '';
                }
            }
        }

        // Initialize with a sample quote
        window.onload = function() {
            getQuote();
            // Auto-connect terminal after 2 seconds
            setTimeout(() => {
                addTerminalLine('Auto-connecting to real-time data stream...', 'info');
                connectTerminal();
            }, 2000);
        };
    </script>
</body>
</html>
