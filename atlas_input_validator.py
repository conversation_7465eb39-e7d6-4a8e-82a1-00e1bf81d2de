"""
A.T.L.A.S. Input Validator
Critical input validation and sanitization for security and data integrity
"""

import re
import logging
from typing import Any, Dict, List, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Custom exception for input validation errors"""
    pass

class InputType(Enum):
    """Types of inputs that can be validated"""
    SYMBOL = "symbol"
    PRICE = "price"
    VOLUME = "volume"
    PERCENTAGE = "percentage"
    DATE = "date"
    EMAIL = "email"
    API_KEY = "api_key"
    SQL_QUERY = "sql_query"
    FILE_PATH = "file_path"
    URL = "url"
    JSON = "json"
    INTEGER = "integer"
    FLOAT = "float"
    STRING = "string"

@dataclass
class ValidationResult:
    """Result of input validation"""
    is_valid: bool
    sanitized_value: Any
    error_message: Optional[str] = None
    warnings: List[str] = None

class AtlasInputValidator:
    """
    Comprehensive input validation and sanitization system
    Protects against injection attacks, malformed data, and invalid inputs
    """
    
    # Regex patterns for validation
    PATTERNS = {
        'symbol': re.compile(r'^[A-Z]{1,5}$'),
        'email': re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
        'api_key': re.compile(r'^[a-zA-Z0-9_-]{10,128}$'),
        'file_path': re.compile(r'^[a-zA-Z0-9._/\\-]{1,255}$'),
        'url': re.compile(r'^https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(/.*)?$'),
        'date_iso': re.compile(r'^\d{4}-\d{2}-\d{2}$'),
        'datetime_iso': re.compile(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$')
    }
    
    # Dangerous patterns to block
    DANGEROUS_PATTERNS = [
        re.compile(r'(union|select|insert|update|delete|drop|create|alter)\s', re.IGNORECASE),
        re.compile(r'<script[^>]*>.*?</script>', re.IGNORECASE | re.DOTALL),
        re.compile(r'javascript:', re.IGNORECASE),
        re.compile(r'on\w+\s*=', re.IGNORECASE),
        re.compile(r'eval\s*\(', re.IGNORECASE),
        re.compile(r'exec\s*\(', re.IGNORECASE),
        re.compile(r'__import__', re.IGNORECASE),
        re.compile(r'subprocess|os\.system|os\.popen', re.IGNORECASE)
    ]
    
    # Maximum lengths for different input types
    MAX_LENGTHS = {
        'symbol': 10,
        'string': 1000,
        'api_key': 128,
        'file_path': 255,
        'url': 2048,
        'email': 254
    }
    
    @classmethod
    def validate_symbol(cls, symbol: str) -> ValidationResult:
        """Validate stock symbol"""
        try:
            if not symbol:
                return ValidationResult(False, None, "Symbol cannot be empty")
            
            # Convert to uppercase and strip whitespace
            symbol = str(symbol).upper().strip()
            
            # Check length
            if len(symbol) > cls.MAX_LENGTHS['symbol']:
                return ValidationResult(False, None, f"Symbol too long: {len(symbol)} > {cls.MAX_LENGTHS['symbol']}")
            
            # Check pattern
            if not cls.PATTERNS['symbol'].match(symbol):
                return ValidationResult(False, None, f"Invalid symbol format: {symbol}")
            
            return ValidationResult(True, symbol)
            
        except Exception as e:
            return ValidationResult(False, None, f"Symbol validation error: {e}")
    
    @classmethod
    def validate_price(cls, price: Union[str, int, float]) -> ValidationResult:
        """Validate price input"""
        try:
            if price is None:
                return ValidationResult(False, None, "Price cannot be None")
            
            # Convert to float
            if isinstance(price, str):
                price = price.strip()
                if not price:
                    return ValidationResult(False, None, "Price cannot be empty")
                
                # Remove currency symbols
                price = re.sub(r'[$,]', '', price)
                
                try:
                    price = float(price)
                except ValueError:
                    return ValidationResult(False, None, f"Invalid price format: {price}")
            
            price = float(price)
            
            # Validate range
            if price < 0:
                return ValidationResult(False, None, "Price cannot be negative")
            
            if price > 1000000:  # $1M max
                return ValidationResult(False, None, "Price exceeds maximum allowed value")
            
            # Round to 4 decimal places
            price = round(price, 4)
            
            return ValidationResult(True, price)
            
        except Exception as e:
            return ValidationResult(False, None, f"Price validation error: {e}")
    
    @classmethod
    def validate_volume(cls, volume: Union[str, int, float]) -> ValidationResult:
        """Validate volume input"""
        try:
            if volume is None:
                return ValidationResult(False, None, "Volume cannot be None")
            
            # Convert to int
            if isinstance(volume, str):
                volume = volume.strip()
                if not volume:
                    return ValidationResult(False, None, "Volume cannot be empty")
                
                # Remove commas
                volume = volume.replace(',', '')
                
                try:
                    volume = int(float(volume))
                except ValueError:
                    return ValidationResult(False, None, f"Invalid volume format: {volume}")
            
            volume = int(volume)
            
            # Validate range
            if volume < 0:
                return ValidationResult(False, None, "Volume cannot be negative")
            
            if volume > 1000000000:  # 1B max
                return ValidationResult(False, None, "Volume exceeds maximum allowed value")
            
            return ValidationResult(True, volume)
            
        except Exception as e:
            return ValidationResult(False, None, f"Volume validation error: {e}")
    
    @classmethod
    def validate_percentage(cls, percentage: Union[str, int, float]) -> ValidationResult:
        """Validate percentage input"""
        try:
            if percentage is None:
                return ValidationResult(False, None, "Percentage cannot be None")
            
            # Convert to float
            if isinstance(percentage, str):
                percentage = percentage.strip()
                if not percentage:
                    return ValidationResult(False, None, "Percentage cannot be empty")
                
                # Remove % symbol
                percentage = percentage.replace('%', '')
                
                try:
                    percentage = float(percentage)
                except ValueError:
                    return ValidationResult(False, None, f"Invalid percentage format: {percentage}")
            
            percentage = float(percentage)
            
            # Validate range (-100% to 1000%)
            if percentage < -100:
                return ValidationResult(False, None, "Percentage cannot be less than -100%")
            
            if percentage > 1000:
                return ValidationResult(False, None, "Percentage cannot exceed 1000%")
            
            # Round to 2 decimal places
            percentage = round(percentage, 2)
            
            return ValidationResult(True, percentage)
            
        except Exception as e:
            return ValidationResult(False, None, f"Percentage validation error: {e}")
    
    @classmethod
    def validate_string(cls, text: str, max_length: int = None, allow_empty: bool = True) -> ValidationResult:
        """Validate string input with security checks"""
        try:
            if text is None:
                if allow_empty:
                    return ValidationResult(True, "")
                else:
                    return ValidationResult(False, None, "String cannot be None")
            
            text = str(text)
            
            # Check for dangerous patterns
            for pattern in cls.DANGEROUS_PATTERNS:
                if pattern.search(text):
                    return ValidationResult(False, None, "Input contains potentially dangerous content")
            
            # Check length
            max_len = max_length or cls.MAX_LENGTHS['string']
            if len(text) > max_len:
                return ValidationResult(False, None, f"String too long: {len(text)} > {max_len}")
            
            # Basic sanitization
            sanitized = text.strip()
            
            return ValidationResult(True, sanitized)
            
        except Exception as e:
            return ValidationResult(False, None, f"String validation error: {e}")
    
    @classmethod
    def validate_integer(cls, value: Union[str, int, float], min_val: int = None, max_val: int = None) -> ValidationResult:
        """Validate integer input"""
        try:
            if value is None:
                return ValidationResult(False, None, "Integer cannot be None")
            
            # Convert to int
            if isinstance(value, str):
                value = value.strip()
                if not value:
                    return ValidationResult(False, None, "Integer cannot be empty")
                
                try:
                    value = int(float(value))
                except ValueError:
                    return ValidationResult(False, None, f"Invalid integer format: {value}")
            
            value = int(value)
            
            # Check bounds
            if min_val is not None and value < min_val:
                return ValidationResult(False, None, f"Integer {value} below minimum {min_val}")
            
            if max_val is not None and value > max_val:
                return ValidationResult(False, None, f"Integer {value} above maximum {max_val}")
            
            return ValidationResult(True, value)
            
        except Exception as e:
            return ValidationResult(False, None, f"Integer validation error: {e}")
    
    @classmethod
    def validate_float(cls, value: Union[str, int, float], min_val: float = None, max_val: float = None) -> ValidationResult:
        """Validate float input"""
        try:
            if value is None:
                return ValidationResult(False, None, "Float cannot be None")
            
            # Convert to float
            if isinstance(value, str):
                value = value.strip()
                if not value:
                    return ValidationResult(False, None, "Float cannot be empty")
                
                try:
                    value = float(value)
                except ValueError:
                    return ValidationResult(False, None, f"Invalid float format: {value}")
            
            value = float(value)
            
            # Check for NaN and infinity
            if not (value == value):  # NaN check
                return ValidationResult(False, None, "Float cannot be NaN")
            
            if value == float('inf') or value == float('-inf'):
                return ValidationResult(False, None, "Float cannot be infinite")
            
            # Check bounds
            if min_val is not None and value < min_val:
                return ValidationResult(False, None, f"Float {value} below minimum {min_val}")
            
            if max_val is not None and value > max_val:
                return ValidationResult(False, None, f"Float {value} above maximum {max_val}")
            
            return ValidationResult(True, value)
            
        except Exception as e:
            return ValidationResult(False, None, f"Float validation error: {e}")
    
    @classmethod
    def validate_api_key(cls, api_key: str) -> ValidationResult:
        """Validate API key format"""
        try:
            if not api_key:
                return ValidationResult(False, None, "API key cannot be empty")
            
            api_key = str(api_key).strip()
            
            # Check length
            if len(api_key) < 10:
                return ValidationResult(False, None, "API key too short")
            
            if len(api_key) > cls.MAX_LENGTHS['api_key']:
                return ValidationResult(False, None, "API key too long")
            
            # Check pattern
            if not cls.PATTERNS['api_key'].match(api_key):
                return ValidationResult(False, None, "Invalid API key format")
            
            return ValidationResult(True, api_key)
            
        except Exception as e:
            return ValidationResult(False, None, f"API key validation error: {e}")
    
    @classmethod
    def sanitize_sql_input(cls, input_str: str) -> str:
        """Sanitize input to prevent SQL injection"""
        if not input_str:
            return ""
        
        # Remove dangerous SQL keywords and characters
        dangerous_chars = ["'", '"', ";", "--", "/*", "*/", "xp_", "sp_"]
        sanitized = str(input_str)
        
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, "")
        
        return sanitized.strip()
    
    @classmethod
    def validate_batch(cls, inputs: Dict[str, Any], validation_rules: Dict[str, InputType]) -> Dict[str, ValidationResult]:
        """Validate multiple inputs at once"""
        results = {}
        
        for field_name, input_value in inputs.items():
            if field_name not in validation_rules:
                results[field_name] = ValidationResult(False, None, f"No validation rule for field: {field_name}")
                continue
            
            input_type = validation_rules[field_name]
            
            # Route to appropriate validation method
            if input_type == InputType.SYMBOL:
                results[field_name] = cls.validate_symbol(input_value)
            elif input_type == InputType.PRICE:
                results[field_name] = cls.validate_price(input_value)
            elif input_type == InputType.VOLUME:
                results[field_name] = cls.validate_volume(input_value)
            elif input_type == InputType.PERCENTAGE:
                results[field_name] = cls.validate_percentage(input_value)
            elif input_type == InputType.INTEGER:
                results[field_name] = cls.validate_integer(input_value)
            elif input_type == InputType.FLOAT:
                results[field_name] = cls.validate_float(input_value)
            elif input_type == InputType.STRING:
                results[field_name] = cls.validate_string(input_value)
            elif input_type == InputType.API_KEY:
                results[field_name] = cls.validate_api_key(input_value)
            else:
                results[field_name] = ValidationResult(False, None, f"Unknown input type: {input_type}")
        
        return results

# Global validator instance
validator = AtlasInputValidator()

# Convenience functions
def validate_symbol(symbol: str) -> ValidationResult:
    """Convenience function for symbol validation"""
    return validator.validate_symbol(symbol)

def validate_price(price: Union[str, int, float]) -> ValidationResult:
    """Convenience function for price validation"""
    return validator.validate_price(price)

def validate_volume(volume: Union[str, int, float]) -> ValidationResult:
    """Convenience function for volume validation"""
    return validator.validate_volume(volume)

def safe_string(text: str, max_length: int = 1000) -> str:
    """Safely sanitize string input"""
    result = validator.validate_string(text, max_length)
    return result.sanitized_value if result.is_valid else ""

# Export main classes and functions
__all__ = [
    "AtlasInputValidator",
    "ValidationError",
    "ValidationResult", 
    "InputType",
    "validator",
    "validate_symbol",
    "validate_price",
    "validate_volume",
    "safe_string"
]
