# 🔍 A.T.L.A.S. Web Search API Setup Guide

The A.T.L.A.S. system includes advanced web search capabilities for enhanced AI context and news analysis. You're absolutely right - we need real search API keys for this to work properly!

## 🚨 **IMPORTANT: Search APIs Required for Full Functionality**

The web search service supports multiple providers with automatic fallback:

### **1. 🆓 DuckDuckGo (No API Key Required)**
- **Provider**: DuckDuckGo Search
- **Cost**: FREE
- **Setup**: Install package only
- **Limitations**: No date filtering, basic results

```bash
pip install duckduckgo-search
```

### **2. 🔍 Google Custom Search API**
- **Provider**: Google Cloud Platform
- **Cost**: 100 free searches/day, then $5/1000 queries
- **Setup Required**: ✅ API Key + Search Engine ID

**Setup Steps:**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable "Custom Search API"
3. Create credentials (API Key)
4. Set up Custom Search Engine at [cse.google.com](https://cse.google.com/)
5. Get your Search Engine ID

```env
GOOGLE_SEARCH_API_KEY=your_google_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here
```

### **3. 🐍 SerpAPI**
- **Provider**: SerpAPI
- **Cost**: 100 free searches/month, then $50/month
- **Setup Required**: ✅ API Key

**Setup Steps:**
1. Sign up at [serpapi.com](https://serpapi.com/)
2. Get your API key from dashboard

```env
SERPAPI_KEY=your_serpapi_key_here
```

### **4. 🔎 SERP API**
- **Provider**: SERP API
- **Cost**: Various plans starting at $29/month
- **Setup Required**: ✅ API Key

**Setup Steps:**
1. Sign up at [serpapi.com](https://serpapi.com/) (different from SerpAPI)
2. Get your API key

```env
SERP_API_KEY=your_serp_api_key_here
```

### **5. 🔵 Bing Search API**
- **Provider**: Microsoft Azure
- **Cost**: 1000 free searches/month, then $3/1000 queries
- **Setup Required**: ✅ API Key

**Setup Steps:**
1. Go to [Azure Portal](https://portal.azure.com/)
2. Create "Bing Search v7" resource
3. Get your API key

```env
BING_SEARCH_API_KEY=your_bing_api_key_here
```

## 📋 **Current .env Configuration**

Update your `.env` file with the search APIs you want to use:

```env
# Web Search APIs (for enhanced functionality)
GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_google_search_engine_id_here
BING_SEARCH_API_KEY=your_bing_search_api_key_here
SERP_API_KEY=your_serp_api_key_here
SERPAPI_KEY=your_serpapi_key_here
DUCKDUCKGO_ENABLED=true
WEB_SEARCH_ENABLED=true
```

## 🎯 **Recommended Setup for Different Use Cases**

### **💰 Budget-Friendly (FREE)**
```env
DUCKDUCKGO_ENABLED=true
WEB_SEARCH_ENABLED=true
# Leave other search APIs empty
```
- Uses DuckDuckGo only
- No API costs
- Basic search functionality

### **🚀 Professional (Recommended)**
```env
GOOGLE_SEARCH_API_KEY=your_google_key
GOOGLE_SEARCH_ENGINE_ID=your_engine_id
SERPAPI_KEY=your_serpapi_key
DUCKDUCKGO_ENABLED=true
WEB_SEARCH_ENABLED=true
```
- Google for high-quality results
- SerpAPI as backup
- DuckDuckGo as final fallback
- Cost: ~$5-50/month depending on usage

### **🏢 Enterprise (Full Coverage)**
```env
GOOGLE_SEARCH_API_KEY=your_google_key
GOOGLE_SEARCH_ENGINE_ID=your_engine_id
BING_SEARCH_API_KEY=your_bing_key
SERP_API_KEY=your_serp_key
SERPAPI_KEY=your_serpapi_key
DUCKDUCKGO_ENABLED=true
WEB_SEARCH_ENABLED=true
```
- All providers configured
- Maximum reliability and coverage
- Cost: $50-200/month depending on usage

## 🔧 **How the Search System Works**

### **Provider Priority Order:**
1. **DuckDuckGo** (if enabled) - Tried first as it's free
2. **Google Custom Search** (if configured) - High quality results
3. **SerpAPI** (if configured) - Professional search API
4. **SERP API** (if configured) - Alternative professional API
5. **Bing Search** (if configured) - Microsoft's search
6. **Mock Results** (fallback) - If all providers fail

### **Automatic Fallback:**
- If one provider fails, automatically tries the next
- Graceful degradation to mock data if all fail
- No system crashes due to search failures

## 📊 **Search Usage in A.T.L.A.S.**

The web search is used for:

### **🤖 AI Context Enhancement**
- Provides real-time market context to Grok AI
- Enhances trading strategy recommendations
- Improves conversation relevance

### **📰 News Sentiment Analysis**
- Searches for recent news about specific stocks
- Analyzes market sentiment from news sources
- Provides context for trading decisions

### **📈 Market Research**
- Technical analysis research
- Earnings information gathering
- Market trend analysis

## ⚙️ **Testing Your Search Setup**

### **1. Test Search Availability**
```python
from atlas_web_search_service import web_search_service

print("Available providers:", web_search_service.available_providers)
print("Search enabled:", web_search_service.is_available())
```

### **2. Test Search Functionality**
```python
import asyncio
from atlas_web_search_service import search_market_news

async def test_search():
    results = await search_market_news("AAPL earnings", ["AAPL"])
    for result in results:
        print(f"Title: {result.title}")
        print(f"Source: {result.source}")
        print(f"URL: {result.url}")
        print("---")

asyncio.run(test_search())
```

## 🚨 **Important Notes**

### **Rate Limits**
- Each provider has different rate limits
- The system includes built-in rate limiting (1 second between searches)
- Monitor your usage to avoid overage charges

### **API Costs**
- Start with free tiers to test functionality
- Monitor usage in provider dashboards
- Set up billing alerts to avoid surprises

### **Fallback Strategy**
- Always enable DuckDuckGo as a free fallback
- The system works even without any paid APIs
- Mock data ensures system never crashes

## 🎯 **Quick Start Recommendation**

**For immediate testing:**
1. Set `DUCKDUCKGO_ENABLED=true` and `WEB_SEARCH_ENABLED=true`
2. Install: `pip install duckduckgo-search`
3. Test the system - it will work with free DuckDuckGo searches

**For production use:**
1. Sign up for Google Custom Search (100 free searches/day)
2. Add your Google API credentials
3. Keep DuckDuckGo as fallback

This gives you professional search capabilities with minimal cost and maximum reliability!

## 📞 **Need Help?**

If you encounter issues:
1. Check the logs for specific error messages
2. Verify your API keys are correct
3. Test each provider individually
4. Ensure you have the required Python packages installed

The search system is designed to be robust - even if all APIs fail, the system continues working with mock data, so your trading operations are never interrupted.
