"""
A.T.L.A.S. Progress Tracker
Real-time operation tracking and progress monitoring system
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)

class OperationType(Enum):
    """Types of operations that can be tracked"""
    MARKET_SCAN = "market_scan"
    NEWS_ANALYSIS = "news_analysis"
    PATTERN_DETECTION = "pattern_detection"
    RISK_ASSESSMENT = "risk_assessment"
    TRADE_ANALYSIS = "trade_analysis"
    DATA_VALIDATION = "data_validation"
    AI_PROCESSING = "ai_processing"
    WEB_SEARCH = "web_search"
    SYSTEM_STARTUP = "system_startup"
    PORTFOLIO_UPDATE = "portfolio_update"

class OperationStatus(Enum):
    """Status of tracked operations"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class ProgressStep:
    """Individual progress step within an operation"""
    step_id: int
    name: str
    description: str
    status: OperationStatus = OperationStatus.PENDING
    progress: float = 0.0  # 0.0 to 1.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class TrackedOperation:
    """A tracked operation with multiple steps"""
    operation_id: str
    operation_type: OperationType
    title: str
    description: str
    status: OperationStatus = OperationStatus.PENDING
    overall_progress: float = 0.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    steps: List[ProgressStep] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    callback: Optional[Callable] = None

class AtlasProgressTracker:
    """
    Real-time progress tracking system for A.T.L.A.S. operations
    Provides detailed progress monitoring and status updates
    """
    
    def __init__(self):
        self.operations: Dict[str, TrackedOperation] = {}
        self.active_operations: Dict[str, TrackedOperation] = {}
        self.completed_operations: List[TrackedOperation] = []
        self.max_completed_history = 100
        
        # Performance metrics
        self.operation_metrics: Dict[OperationType, Dict[str, Any]] = {}
        
        logger.info("Progress tracker initialized")
    
    def create_operation(self, operation_type: OperationType, title: str, 
                        description: str, steps: List[str] = None,
                        callback: Callable = None) -> str:
        """
        Create a new tracked operation
        """
        try:
            operation_id = str(uuid.uuid4())
            
            # Create progress steps
            progress_steps = []
            if steps:
                for i, step_name in enumerate(steps):
                    progress_steps.append(ProgressStep(
                        step_id=i,
                        name=step_name,
                        description=f"Executing {step_name}"
                    ))
            
            operation = TrackedOperation(
                operation_id=operation_id,
                operation_type=operation_type,
                title=title,
                description=description,
                steps=progress_steps,
                callback=callback
            )
            
            self.operations[operation_id] = operation
            self.active_operations[operation_id] = operation
            
            logger.debug(f"Created operation {operation_id}: {title}")
            return operation_id
            
        except Exception as e:
            logger.error(f"Failed to create operation: {e}")
            return None
    
    async def start_operation(self, operation_id: str) -> bool:
        """
        Start tracking an operation
        """
        try:
            if operation_id not in self.operations:
                logger.error(f"Operation {operation_id} not found")
                return False
            
            operation = self.operations[operation_id]
            operation.status = OperationStatus.IN_PROGRESS
            operation.start_time = datetime.now()
            
            # Initialize metrics for this operation type
            if operation.operation_type not in self.operation_metrics:
                self.operation_metrics[operation.operation_type] = {
                    'total_count': 0,
                    'success_count': 0,
                    'failure_count': 0,
                    'average_duration': 0.0,
                    'total_duration': 0.0
                }
            
            self.operation_metrics[operation.operation_type]['total_count'] += 1
            
            logger.info(f"Started operation {operation_id}: {operation.title}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start operation {operation_id}: {e}")
            return False
    
    async def start_step(self, operation_id: str, step_id: int) -> bool:
        """
        Start a specific step within an operation
        """
        try:
            if operation_id not in self.operations:
                return False
            
            operation = self.operations[operation_id]
            
            if step_id >= len(operation.steps):
                logger.error(f"Step {step_id} not found in operation {operation_id}")
                return False
            
            step = operation.steps[step_id]
            step.status = OperationStatus.IN_PROGRESS
            step.start_time = datetime.now()
            
            # Update overall progress
            await self._update_overall_progress(operation_id)
            
            logger.debug(f"Started step {step_id} in operation {operation_id}: {step.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start step {step_id} in operation {operation_id}: {e}")
            return False
    
    async def update_step_progress(self, operation_id: str, step_id: int, 
                                 progress: float, message: str = None) -> bool:
        """
        Update progress for a specific step
        """
        try:
            if operation_id not in self.operations:
                return False
            
            operation = self.operations[operation_id]
            
            if step_id >= len(operation.steps):
                return False
            
            step = operation.steps[step_id]
            step.progress = max(0.0, min(1.0, progress))  # Clamp between 0 and 1
            
            if message:
                step.description = message
            
            # Update overall progress
            await self._update_overall_progress(operation_id)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update step progress: {e}")
            return False
    
    async def complete_step(self, operation_id: str, step_id: int, 
                          result_data: Dict[str, Any] = None) -> bool:
        """
        Mark a step as completed
        """
        try:
            if operation_id not in self.operations:
                return False
            
            operation = self.operations[operation_id]
            
            if step_id >= len(operation.steps):
                return False
            
            step = operation.steps[step_id]
            step.status = OperationStatus.COMPLETED
            step.progress = 1.0
            step.end_time = datetime.now()
            
            if result_data:
                step.metadata.update(result_data)
            
            # Update overall progress
            await self._update_overall_progress(operation_id)
            
            # Check if all steps are completed
            if all(s.status == OperationStatus.COMPLETED for s in operation.steps):
                await self.complete_operation(operation_id)
            
            logger.debug(f"Completed step {step_id} in operation {operation_id}: {step.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to complete step {step_id} in operation {operation_id}: {e}")
            return False
    
    async def fail_step(self, operation_id: str, step_id: int, error_message: str) -> bool:
        """
        Mark a step as failed
        """
        try:
            if operation_id not in self.operations:
                return False
            
            operation = self.operations[operation_id]
            
            if step_id >= len(operation.steps):
                return False
            
            step = operation.steps[step_id]
            step.status = OperationStatus.FAILED
            step.end_time = datetime.now()
            step.error_message = error_message
            
            # Fail the entire operation
            await self.fail_operation(operation_id, f"Step {step_id} failed: {error_message}")
            
            logger.error(f"Failed step {step_id} in operation {operation_id}: {error_message}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to fail step {step_id} in operation {operation_id}: {e}")
            return False
    
    async def complete_operation(self, operation_id: str, result_data: Dict[str, Any] = None) -> bool:
        """
        Mark an operation as completed
        """
        try:
            if operation_id not in self.operations:
                return False
            
            operation = self.operations[operation_id]
            operation.status = OperationStatus.COMPLETED
            operation.end_time = datetime.now()
            operation.overall_progress = 1.0
            
            if result_data:
                operation.metadata.update(result_data)
            
            # Update metrics
            duration = (operation.end_time - operation.start_time).total_seconds()
            metrics = self.operation_metrics[operation.operation_type]
            metrics['success_count'] += 1
            metrics['total_duration'] += duration
            metrics['average_duration'] = metrics['total_duration'] / metrics['total_count']
            
            # Move to completed operations
            self._move_to_completed(operation_id)
            
            # Execute callback if provided
            if operation.callback:
                try:
                    await operation.callback(operation)
                except Exception as e:
                    logger.error(f"Operation callback failed: {e}")
            
            logger.info(f"Completed operation {operation_id}: {operation.title} in {duration:.2f}s")
            return True
            
        except Exception as e:
            logger.error(f"Failed to complete operation {operation_id}: {e}")
            return False
    
    async def fail_operation(self, operation_id: str, error_message: str) -> bool:
        """
        Mark an operation as failed
        """
        try:
            if operation_id not in self.operations:
                return False
            
            operation = self.operations[operation_id]
            operation.status = OperationStatus.FAILED
            operation.end_time = datetime.now()
            operation.metadata['error_message'] = error_message
            
            # Update metrics
            self.operation_metrics[operation.operation_type]['failure_count'] += 1
            
            # Move to completed operations
            self._move_to_completed(operation_id)
            
            logger.error(f"Failed operation {operation_id}: {error_message}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to fail operation {operation_id}: {e}")
            return False
    
    async def cancel_operation(self, operation_id: str) -> bool:
        """
        Cancel an operation
        """
        try:
            if operation_id not in self.operations:
                return False
            
            operation = self.operations[operation_id]
            operation.status = OperationStatus.CANCELLED
            operation.end_time = datetime.now()
            
            # Cancel all pending/in-progress steps
            for step in operation.steps:
                if step.status in [OperationStatus.PENDING, OperationStatus.IN_PROGRESS]:
                    step.status = OperationStatus.CANCELLED
                    step.end_time = datetime.now()
            
            # Move to completed operations
            self._move_to_completed(operation_id)
            
            logger.info(f"Cancelled operation {operation_id}: {operation.title}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel operation {operation_id}: {e}")
            return False
    
    def get_operation_status(self, operation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get current status of an operation
        """
        try:
            if operation_id not in self.operations:
                return None
            
            operation = self.operations[operation_id]
            
            return {
                'operation_id': operation.operation_id,
                'operation_type': operation.operation_type.value,
                'title': operation.title,
                'description': operation.description,
                'status': operation.status.value,
                'overall_progress': operation.overall_progress,
                'start_time': operation.start_time.isoformat() if operation.start_time else None,
                'end_time': operation.end_time.isoformat() if operation.end_time else None,
                'steps': [
                    {
                        'step_id': step.step_id,
                        'name': step.name,
                        'description': step.description,
                        'status': step.status.value,
                        'progress': step.progress,
                        'error_message': step.error_message
                    }
                    for step in operation.steps
                ],
                'metadata': operation.metadata
            }
            
        except Exception as e:
            logger.error(f"Failed to get operation status for {operation_id}: {e}")
            return None
    
    def get_active_operations(self) -> List[Dict[str, Any]]:
        """
        Get all active operations
        """
        try:
            return [
                self.get_operation_status(op_id) 
                for op_id in self.active_operations.keys()
            ]
        except Exception as e:
            logger.error(f"Failed to get active operations: {e}")
            return []
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics
        """
        try:
            return {
                'active_operations_count': len(self.active_operations),
                'completed_operations_count': len(self.completed_operations),
                'total_operations_count': len(self.operations),
                'operation_type_metrics': {
                    op_type.value: metrics 
                    for op_type, metrics in self.operation_metrics.items()
                }
            }
        except Exception as e:
            logger.error(f"Failed to get metrics: {e}")
            return {}
    
    async def _update_overall_progress(self, operation_id: str):
        """
        Update overall progress based on step progress
        """
        try:
            operation = self.operations[operation_id]
            
            if not operation.steps:
                return
            
            total_progress = sum(step.progress for step in operation.steps)
            operation.overall_progress = total_progress / len(operation.steps)
            
        except Exception as e:
            logger.error(f"Failed to update overall progress for {operation_id}: {e}")
    
    def _move_to_completed(self, operation_id: str):
        """
        Move operation from active to completed
        """
        try:
            if operation_id in self.active_operations:
                operation = self.active_operations.pop(operation_id)
                self.completed_operations.append(operation)
                
                # Maintain history limit
                if len(self.completed_operations) > self.max_completed_history:
                    self.completed_operations.pop(0)
                    
        except Exception as e:
            logger.error(f"Failed to move operation {operation_id} to completed: {e}")
    
    def cleanup_old_operations(self, max_age_hours: int = 24):
        """
        Clean up old completed operations
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            
            # Remove old completed operations
            self.completed_operations = [
                op for op in self.completed_operations
                if op.end_time and op.end_time > cutoff_time
            ]
            
            logger.info(f"Cleaned up operations older than {max_age_hours} hours")
            
        except Exception as e:
            logger.error(f"Failed to cleanup old operations: {e}")

# Global progress tracker instance
progress_tracker = AtlasProgressTracker()

# Convenience functions
def create_operation(operation_type: OperationType, title: str, 
                    description: str, steps: List[str] = None) -> str:
    """Create a new tracked operation"""
    return progress_tracker.create_operation(operation_type, title, description, steps)

async def track_operation(operation_type: OperationType, title: str, 
                         description: str, steps: List[str] = None):
    """Context manager for tracking operations"""
    operation_id = create_operation(operation_type, title, description, steps)
    await progress_tracker.start_operation(operation_id)
    return operation_id

# Export main classes and functions
__all__ = [
    "AtlasProgressTracker",
    "OperationType",
    "OperationStatus", 
    "ProgressStep",
    "TrackedOperation",
    "progress_tracker",
    "create_operation",
    "track_operation"
]
