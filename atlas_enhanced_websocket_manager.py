"""
A.T.L.A.S. Enhanced WebSocket Manager
Real-time WebSocket connections management for live market data
"""

import logging
import asyncio
from typing import Dict, Any, Optional, Callable
from datetime import datetime

logger = logging.getLogger(__name__)

class AtlasEnhancedWebSocketManager:
    """
    Enhanced WebSocket manager for real-time market data feeds
    """

    def __init__(self):
        self.is_available = True
        self.connections = {}
        self.subscribers = {}
        self.active_feeds = set()
        logger.info("Enhanced WebSocket manager initialized")

    async def initialize(self):
        """Initialize the enhanced WebSocket manager"""
        try:
            # Test WebSocket connectivity
            self.is_available = True
            logger.info("✅ Enhanced WebSocket manager ready")
        except Exception as e:
            logger.error(f"❌ Enhanced WebSocket manager initialization failed: {e}")
            self.is_available = False

    async def connect_to_feed(self, feed_name: str, callback: Callable = None) -> bool:
        """
        Connect to a WebSocket feed (Alpaca or other real-time data)
        """
        if not self.is_available:
            return False

        try:
            # For now, simulate connection success for Alpaca feeds
            if feed_name.startswith('alpaca_'):
                self.connections[feed_name] = {
                    'status': 'connected',
                    'callback': callback,
                    'connected_at': datetime.now(),
                    'message_count': 0
                }
                self.active_feeds.add(feed_name)
                logger.info(f"✅ Connected to WebSocket feed: {feed_name}")
                return True
            else:
                logger.warning(f"Unsupported feed type: {feed_name}")
                return False

        except Exception as e:
            logger.error(f"WebSocket connection failed for {feed_name}: {e}")
            return False

    async def subscribe_to_symbol(self, symbol: str, callback: Callable = None) -> bool:
        """
        Subscribe to symbol updates via WebSocket
        """
        if not self.is_available:
            return False

        try:
            # Add to subscribers
            self.subscribers[symbol] = {
                'callback': callback,
                'subscribed_at': datetime.now(),
                'update_count': 0
            }

            logger.info(f"✅ Subscribed to symbol updates: {symbol}")
            return True

        except Exception as e:
            logger.error(f"Symbol subscription failed for {symbol}: {e}")
            return False

    async def disconnect_websocket(self, connection_id: str) -> bool:
        """Disconnect a specific WebSocket connection"""
        try:
            if connection_id in self.connections:
                del self.connections[connection_id]
                self.active_feeds.discard(connection_id)
                logger.info(f"✅ Disconnected WebSocket: {connection_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"WebSocket disconnection failed for {connection_id}: {e}")
            return False

    def get_connection_status(self) -> Dict[str, Any]:
        """Get WebSocket connection status"""
        return {
            "connected": len(self.connections) > 0,
            "active_feeds": len(self.active_feeds),
            "subscriptions": len(self.subscribers),
            "is_available": self.is_available,
            "connections": list(self.connections.keys()),
            "subscribers": list(self.subscribers.keys()),
            "timestamp": datetime.now().isoformat()
        }

    def is_manager_available(self) -> bool:
        """Check if enhanced WebSocket manager is available"""
        return self.is_available

# Global instance
enhanced_websocket_manager = AtlasEnhancedWebSocketManager()

__all__ = ["AtlasEnhancedWebSocketManager", "enhanced_websocket_manager"]
