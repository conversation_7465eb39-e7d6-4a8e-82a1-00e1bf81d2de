"""
A.T.L.A.S. Trade Execution Agent
6-point trading recommendation system with real-time execution analysis
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from atlas_multi_agent_core import (
    AtlasBaseAgent, AgentRole, AgentCapabilities, MultiAgentTask
)
from atlas_enhanced_market_data import AtlasEnhancedMarketDataEngine
from atlas_trading_core import AtlasTradingEngine

logger = logging.getLogger(__name__)

@dataclass
class TradingRecommendation:
    """6-point trading recommendation structure"""
    symbol: str
    action: str  # BUY, SELL, HOLD
    entry_price: float
    target_price: float
    stop_loss: float
    position_size: int
    confidence: float
    risk_reward_ratio: float
    reasoning: List[str]
    timestamp: datetime

class AtlasTradeExecutionAgent(AtlasBaseAgent):
    """Trade execution agent for 6-point trading recommendations"""

    def __init__(self):
        super().__init__(AgentRole.TRADE_EXECUTOR)
        self.trading_engine = None
        self.market_data_engine = None

    def _define_capabilities(self) -> AgentCapabilities:
        """Define trade execution agent capabilities"""
        return AgentCapabilities(
            role=AgentRole.TRADE_EXECUTOR,
            supported_tasks=[
                "generate_trading_recommendation",
                "calculate_position_sizing",
                "validate_trade_setup",
                "monitor_trade_execution",
                "update_stop_loss",
                "calculate_risk_reward"
            ],
            max_concurrent_tasks=2,
            average_processing_time=15.0,
            success_rate=0.90,
            dependencies=[AgentRole.ANALYSIS_ENGINE, AgentRole.RISK_MANAGER]
        )

    async def _initialize_agent(self):
        """Initialize the trade execution agent"""
        try:
            # Initialize trading engine
            self.trading_engine = AtlasTradingEngine()
            await self.trading_engine.initialize()

            # Initialize market data engine
            self.market_data_engine = AtlasEnhancedMarketDataEngine()
            await self.market_data_engine.initialize()

            self.logger.info("Trade execution agent initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize trade execution agent: {e}")
            raise

    async def execute_task(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Execute a trade execution task"""
        try:
            task_type = task.input_data.get("task_type", "generate_trading_recommendation")
            symbol = task.input_data.get("symbol", "")

            if task_type == "generate_trading_recommendation":
                return await self._generate_trading_recommendation(symbol, task.input_data)
            elif task_type == "calculate_position_sizing":
                return await self._calculate_position_sizing(symbol, task.input_data)
            elif task_type == "validate_trade_setup":
                return await self._validate_trade_setup(symbol, task.input_data)
            elif task_type == "monitor_trade_execution":
                return await self._monitor_trade_execution(symbol, task.input_data)
            elif task_type == "update_stop_loss":
                return await self._update_stop_loss(symbol, task.input_data)
            elif task_type == "calculate_risk_reward":
                return await self._calculate_risk_reward(symbol, task.input_data)
            else:
                return {"error": f"Unknown task type: {task_type}"}

        except Exception as e:
            self.logger.error(f"Trade execution task failed: {e}")
            self.failed_tasks += 1
            return {"error": str(e)}

    async def _generate_trading_recommendation(self, symbol: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive 6-point trading recommendation"""
        try:
            # Get market data
            market_data = await self.market_data_engine.get_real_time_quote(symbol)

            if not market_data:
                return {"error": "No market data available", "symbol": symbol}

            current_price = market_data.get("price", 0)

            # Get analysis results from other agents
            analysis_result = params.get("analysis_result", {})
            risk_assessment = params.get("risk_assessment", {})
            pattern_detection = params.get("pattern_detection", {})

            # Extract key metrics
            composite_score = analysis_result.get("composite_score", 0.5)
            sentiment_score = analysis_result.get("component_scores", {}).get("sentiment", 0.5)
            technical_score = analysis_result.get("component_scores", {}).get("technical", 0.5)
            risk_score = risk_assessment.get("risk_score", 50)
            pattern_detected = pattern_detection.get("pattern_detected", False)
            pattern_strength = pattern_detection.get("strength", 0.0)

            # 1. DETERMINE ACTION (BUY/SELL/HOLD)
            action = self._determine_action(composite_score, sentiment_score, technical_score, pattern_detected)

            # 2. CALCULATE ENTRY PRICE
            entry_price = self._calculate_entry_price(current_price, action, market_data)

            # 3. CALCULATE TARGET PRICE
            target_price = self._calculate_target_price(entry_price, action, composite_score, pattern_strength)

            # 4. CALCULATE STOP LOSS
            stop_loss = self._calculate_stop_loss(entry_price, action, risk_score, market_data)

            # 5. CALCULATE POSITION SIZE
            account_value = params.get("account_value", 100000)
            position_size = self._calculate_optimal_position_size(
                entry_price, stop_loss, account_value, risk_score
            )

            # 6. CALCULATE CONFIDENCE & RISK/REWARD
            confidence = self._calculate_confidence(
                composite_score, pattern_detected, pattern_strength, risk_score
            )

            risk_reward_ratio = self._calculate_risk_reward_ratio(entry_price, target_price, stop_loss, action)

            # Generate reasoning
            reasoning = self._generate_reasoning(
                action, composite_score, sentiment_score, technical_score,
                pattern_detected, risk_score, risk_reward_ratio
            )

            # Validate recommendation
            is_valid, validation_issues = self._validate_recommendation(
                action, entry_price, target_price, stop_loss, risk_reward_ratio, confidence
            )

            self.completed_tasks += 1
            return {
                "symbol": symbol,
                "recommendation": {
                    "action": action,
                    "entry_price": entry_price,
                    "target_price": target_price,
                    "stop_loss": stop_loss,
                    "position_size": position_size,
                    "confidence": confidence,
                    "risk_reward_ratio": risk_reward_ratio
                },
                "analysis": {
                    "current_price": current_price,
                    "composite_score": composite_score,
                    "sentiment_score": sentiment_score,
                    "technical_score": technical_score,
                    "risk_score": risk_score,
                    "pattern_detected": pattern_detected,
                    "pattern_strength": pattern_strength
                },
                "reasoning": reasoning,
                "validation": {
                    "is_valid": is_valid,
                    "issues": validation_issues
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Trading recommendation generation failed for {symbol}: {e}")
            return {"error": str(e), "symbol": symbol}

    def _determine_action(self, composite_score: float, sentiment_score: float,
                         technical_score: float, pattern_detected: bool) -> str:
        """Determine trading action based on analysis"""

        # Strong buy conditions
        if (composite_score >= 0.75 and
            sentiment_score >= 0.6 and
            technical_score >= 0.6 and
            pattern_detected):
            return "STRONG_BUY"

        # Buy conditions
        elif (composite_score >= 0.6 and
              (sentiment_score >= 0.6 or technical_score >= 0.6)):
            return "BUY"

        # Strong sell conditions
        elif (composite_score <= 0.25 and
              sentiment_score <= 0.4 and
              technical_score <= 0.4):
            return "STRONG_SELL"

        # Sell conditions
        elif composite_score <= 0.4 and sentiment_score <= 0.4:
            return "SELL"

        # Default to hold
        else:
            return "HOLD"

    def _calculate_entry_price(self, current_price: float, action: str, market_data: Dict[str, Any]) -> float:
        """Calculate optimal entry price"""

        if action in ["STRONG_BUY", "BUY"]:
            # For buys, consider entering slightly above current price to ensure execution
            bid_ask_spread = market_data.get("ask", current_price) - market_data.get("bid", current_price)
            return current_price + (bid_ask_spread * 0.5)

        elif action in ["STRONG_SELL", "SELL"]:
            # For sells, consider entering slightly below current price
            bid_ask_spread = market_data.get("ask", current_price) - market_data.get("bid", current_price)
            return current_price - (bid_ask_spread * 0.5)

        else:  # HOLD
            return current_price

    def _calculate_target_price(self, entry_price: float, action: str,
                              composite_score: float, pattern_strength: float) -> float:
        """Calculate target price based on analysis strength"""

        if action in ["STRONG_BUY", "BUY"]:
            # Calculate upside target based on strength
            base_target = 0.05  # 5% base target

            # Adjust based on composite score
            score_multiplier = (composite_score - 0.5) * 2  # Scale to -1 to 1

            # Adjust based on pattern strength
            pattern_multiplier = pattern_strength * 0.5

            # Total target percentage
            target_percent = base_target + (score_multiplier * 0.05) + (pattern_multiplier * 0.03)
            target_percent = max(0.02, min(target_percent, 0.15))  # Cap between 2% and 15%

            return entry_price * (1 + target_percent)

        elif action in ["STRONG_SELL", "SELL"]:
            # Calculate downside target
            base_target = 0.05  # 5% base target

            # Adjust based on composite score (lower score = higher downside)
            score_multiplier = (0.5 - composite_score) * 2

            # Total target percentage
            target_percent = base_target + (score_multiplier * 0.05)
            target_percent = max(0.02, min(target_percent, 0.15))

            return entry_price * (1 - target_percent)

        else:  # HOLD
            return entry_price

    def _calculate_stop_loss(self, entry_price: float, action: str,
                           risk_score: float, market_data: Dict[str, Any]) -> float:
        """Calculate stop loss based on risk assessment"""

        # Base stop loss percentage
        base_stop = 0.03  # 3% base stop loss

        # Adjust based on risk score (higher risk = tighter stop)
        risk_multiplier = risk_score / 100.0  # Convert to 0-1 scale
        adjusted_stop = base_stop + (risk_multiplier * 0.02)  # Add up to 2% for high risk

        # Consider volatility
        volatility_proxy = abs(market_data.get("change_percent", 2.0)) / 100.0
        volatility_adjustment = volatility_proxy * 0.5

        final_stop = adjusted_stop + volatility_adjustment
        final_stop = max(0.02, min(final_stop, 0.08))  # Cap between 2% and 8%

        if action in ["STRONG_BUY", "BUY"]:
            return entry_price * (1 - final_stop)
        elif action in ["STRONG_SELL", "SELL"]:
            return entry_price * (1 + final_stop)
        else:  # HOLD
            return entry_price * (1 - final_stop)  # Default protective stop

    def _calculate_optimal_position_size(self, entry_price: float, stop_loss: float,
                                       account_value: float, risk_score: float) -> int:
        """Calculate optimal position size based on risk management"""

        # Risk per trade (adjust based on risk score)
        base_risk = 0.02  # 2% base risk

        # Lower risk for higher risk scores
        risk_adjustment = (100 - risk_score) / 100.0  # Inverse relationship
        risk_per_trade = base_risk * risk_adjustment
        risk_per_trade = max(0.005, min(risk_per_trade, 0.03))  # Cap between 0.5% and 3%

        # Calculate position size
        risk_amount = account_value * risk_per_trade
        stop_loss_amount = abs(entry_price - stop_loss)

        if stop_loss_amount > 0:
            position_value = risk_amount / (stop_loss_amount / entry_price)
            shares = int(position_value / entry_price)
            return max(1, shares)  # At least 1 share

        return 1

    def _calculate_confidence(self, composite_score: float, pattern_detected: bool,
                            pattern_strength: float, risk_score: float) -> float:
        """Calculate overall confidence in the recommendation"""

        # Base confidence from composite score
        base_confidence = composite_score

        # Pattern detection bonus
        pattern_bonus = 0.0
        if pattern_detected:
            pattern_bonus = pattern_strength * 0.2

        # Risk penalty (higher risk = lower confidence)
        risk_penalty = (risk_score / 100.0) * 0.1

        # Calculate final confidence
        confidence = base_confidence + pattern_bonus - risk_penalty

        return max(0.1, min(confidence, 0.95))  # Cap between 10% and 95%

    def _calculate_risk_reward_ratio(self, entry_price: float, target_price: float,
                                   stop_loss: float, action: str) -> float:
        """Calculate risk/reward ratio"""

        if action in ["STRONG_BUY", "BUY"]:
            potential_reward = target_price - entry_price
            potential_risk = entry_price - stop_loss
        elif action in ["STRONG_SELL", "SELL"]:
            potential_reward = entry_price - target_price
            potential_risk = stop_loss - entry_price
        else:  # HOLD
            return 0.0

        if potential_risk > 0:
            return potential_reward / potential_risk

        return 0.0

    def _generate_reasoning(self, action: str, composite_score: float, sentiment_score: float,
                          technical_score: float, pattern_detected: bool, risk_score: float,
                          risk_reward_ratio: float) -> List[str]:
        """Generate reasoning for the recommendation"""

        reasons = []

        # Action reasoning
        if action in ["STRONG_BUY", "BUY"]:
            reasons.append(f"Bullish recommendation based on {composite_score:.1%} composite score")

            if sentiment_score >= 0.6:
                reasons.append(f"Positive market sentiment ({sentiment_score:.1%})")

            if technical_score >= 0.6:
                reasons.append(f"Strong technical indicators ({technical_score:.1%})")

            if pattern_detected:
                reasons.append("Lee Method pattern detected with 3-criteria validation")

        elif action in ["STRONG_SELL", "SELL"]:
            reasons.append(f"Bearish recommendation based on {composite_score:.1%} composite score")

            if sentiment_score <= 0.4:
                reasons.append(f"Negative market sentiment ({sentiment_score:.1%})")

            if technical_score <= 0.4:
                reasons.append(f"Weak technical indicators ({technical_score:.1%})")

        else:  # HOLD
            reasons.append("Neutral market conditions suggest holding position")

        # Risk reasoning
        if risk_score > 70:
            reasons.append(f"High risk profile ({risk_score}/100) requires careful position sizing")
        elif risk_score < 30:
            reasons.append(f"Low risk profile ({risk_score}/100) allows for larger positions")

        # Risk/reward reasoning
        if risk_reward_ratio >= 2.0:
            reasons.append(f"Favorable risk/reward ratio of {risk_reward_ratio:.1f}:1")
        elif risk_reward_ratio < 1.0:
            reasons.append(f"Unfavorable risk/reward ratio of {risk_reward_ratio:.1f}:1")

        return reasons

    def _validate_recommendation(self, action: str, entry_price: float, target_price: float,
                               stop_loss: float, risk_reward_ratio: float, confidence: float) -> tuple:
        """Validate the trading recommendation"""

        issues = []

        # Check price relationships
        if action in ["STRONG_BUY", "BUY"]:
            if target_price <= entry_price:
                issues.append("Target price should be above entry price for buy orders")
            if stop_loss >= entry_price:
                issues.append("Stop loss should be below entry price for buy orders")

        elif action in ["STRONG_SELL", "SELL"]:
            if target_price >= entry_price:
                issues.append("Target price should be below entry price for sell orders")
            if stop_loss <= entry_price:
                issues.append("Stop loss should be above entry price for sell orders")

        # Check risk/reward ratio
        if risk_reward_ratio < 1.0 and action != "HOLD":
            issues.append(f"Poor risk/reward ratio: {risk_reward_ratio:.2f}:1")

        # Check confidence
        if confidence < 0.3:
            issues.append(f"Low confidence level: {confidence:.1%}")

        # Check for valid prices
        if any(price <= 0 for price in [entry_price, target_price, stop_loss]):
            issues.append("Invalid price values detected")

        is_valid = len(issues) == 0

        return is_valid, issues

    async def _calculate_position_sizing(self, symbol: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate position sizing for a trade"""
        # This would be similar to the position sizing in the main recommendation
        # but can be called independently
        return {"message": "Position sizing calculated", "symbol": symbol}

    async def _validate_trade_setup(self, symbol: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate a trade setup before execution"""
        return {"valid": True, "symbol": symbol}

    async def _monitor_trade_execution(self, symbol: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Monitor trade execution progress"""
        return {"status": "monitoring", "symbol": symbol}

    async def _update_stop_loss(self, symbol: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Update stop loss for an active position"""
        return {"updated": True, "symbol": symbol}

    async def _calculate_risk_reward(self, symbol: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate risk/reward ratio for a trade setup"""
        return {"risk_reward_ratio": 2.0, "symbol": symbol}

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = ["AtlasTradeExecutionAgent", "TradingRecommendation"]