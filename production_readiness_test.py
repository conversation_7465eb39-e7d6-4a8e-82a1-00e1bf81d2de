#!/usr/bin/env python3
"""
A.T.L.A.S. Production Readiness Test
Comprehensive test to validate system functionality with real data
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

class ProductionReadinessTest:
    """Test suite for production readiness validation"""
    
    def __init__(self):
        self.base_url = "http://localhost:8002"
        self.test_results = []
        self.session = None
    
    async def run_all_tests(self):
        """Run comprehensive production readiness tests"""
        print("🚀 A.T.L.A.S. Production Readiness Test Suite")
        print("=" * 60)
        
        async with aiohttp.ClientSession() as session:
            self.session = session
            
            # Test 1: System Health
            await self.test_system_health()
            
            # Test 2: Real Market Data Integration
            await self.test_market_data_integration()
            
            # Test 3: AI Processing with Real Data
            await self.test_ai_processing()
            
            # Test 4: Profit Scanning with Live Data
            await self.test_profit_scanning()
            
            # Test 5: Error Handling
            await self.test_error_handling()
            
            # Test 6: Performance Validation
            await self.test_performance()
        
        # Generate final report
        self.generate_report()
    
    async def test_system_health(self):
        """Test system health endpoint"""
        print("\n📊 Test 1: System Health Check")
        try:
            async with self.session.get(f"{self.base_url}/api/v1/health") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('status') == 'healthy':
                        self.test_results.append(("System Health", "PASS", "System is healthy"))
                        print("✅ System health check passed")
                    else:
                        self.test_results.append(("System Health", "FAIL", f"Unhealthy status: {data.get('status')}"))
                        print("❌ System health check failed")
                else:
                    self.test_results.append(("System Health", "FAIL", f"HTTP {response.status}"))
                    print(f"❌ Health endpoint returned {response.status}")
        except Exception as e:
            self.test_results.append(("System Health", "ERROR", str(e)))
            print(f"❌ Health check error: {e}")
    
    async def test_market_data_integration(self):
        """Test real market data integration"""
        print("\n📈 Test 2: Real Market Data Integration")
        try:
            payload = {
                "message": "What is the current price of AAPL?",
                "session_id": "test_market_data"
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v1/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    response_text = data.get('response', '')
                    
                    # Check for real price data (should contain dollar amounts)
                    if '$' in response_text and any(char.isdigit() for char in response_text):
                        self.test_results.append(("Market Data", "PASS", "Real market data detected"))
                        print("✅ Real market data integration working")
                    else:
                        self.test_results.append(("Market Data", "FAIL", "No real market data detected"))
                        print("❌ No real market data detected")
                else:
                    self.test_results.append(("Market Data", "FAIL", f"HTTP {response.status}"))
                    print(f"❌ Market data test failed with {response.status}")
        except Exception as e:
            self.test_results.append(("Market Data", "ERROR", str(e)))
            print(f"❌ Market data test error: {e}")
    
    async def test_ai_processing(self):
        """Test AI processing with real data"""
        print("\n🧠 Test 3: AI Processing with Real Data")
        try:
            payload = {
                "message": "Analyze TSLA stock for trading opportunities",
                "session_id": "test_ai_processing"
            }
            
            start_time = time.time()
            async with self.session.post(
                f"{self.base_url}/api/v1/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                processing_time = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    
                    # Check for AI-powered analysis
                    if data.get('grok_powered') and data.get('confidence', 0) > 0.5:
                        self.test_results.append(("AI Processing", "PASS", f"AI analysis completed in {processing_time:.2f}s"))
                        print(f"✅ AI processing working (confidence: {data.get('confidence'):.2f})")
                    else:
                        self.test_results.append(("AI Processing", "FAIL", "Low confidence or not Grok-powered"))
                        print("❌ AI processing quality issues")
                else:
                    self.test_results.append(("AI Processing", "FAIL", f"HTTP {response.status}"))
                    print(f"❌ AI processing failed with {response.status}")
        except Exception as e:
            self.test_results.append(("AI Processing", "ERROR", str(e)))
            print(f"❌ AI processing error: {e}")
    
    async def test_profit_scanning(self):
        """Test profit scanning with live market data"""
        print("\n💰 Test 4: Profit Scanning with Live Data")
        try:
            payload = {
                "message": "Find me opportunities to make $50 today",
                "session_id": "test_profit_scanning"
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v1/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    response_text = data.get('response', '')
                    
                    # Check for profit opportunities with real data
                    if 'LIVE OPPORTUNITIES FOUND' in response_text and '$' in response_text:
                        self.test_results.append(("Profit Scanning", "PASS", "Live opportunities detected"))
                        print("✅ Profit scanning with live data working")
                    else:
                        self.test_results.append(("Profit Scanning", "FAIL", "No live opportunities detected"))
                        print("❌ Profit scanning not finding live opportunities")
                else:
                    self.test_results.append(("Profit Scanning", "FAIL", f"HTTP {response.status}"))
                    print(f"❌ Profit scanning failed with {response.status}")
        except Exception as e:
            self.test_results.append(("Profit Scanning", "ERROR", str(e)))
            print(f"❌ Profit scanning error: {e}")
    
    async def test_error_handling(self):
        """Test error handling"""
        print("\n🛡️ Test 5: Error Handling")
        try:
            # Test with invalid input
            payload = {
                "message": "",  # Empty message
                "session_id": "test_error_handling"
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v1/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 400:  # Should return bad request
                    self.test_results.append(("Error Handling", "PASS", "Proper error handling for invalid input"))
                    print("✅ Error handling working correctly")
                else:
                    self.test_results.append(("Error Handling", "FAIL", f"Unexpected status: {response.status}"))
                    print(f"❌ Error handling issue: got {response.status} instead of 400")
        except Exception as e:
            self.test_results.append(("Error Handling", "ERROR", str(e)))
            print(f"❌ Error handling test error: {e}")
    
    async def test_performance(self):
        """Test system performance"""
        print("\n⚡ Test 6: Performance Validation")
        try:
            payload = {
                "message": "Quick analysis of AAPL",
                "session_id": "test_performance"
            }
            
            start_time = time.time()
            async with self.session.post(
                f"{self.base_url}/api/v1/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                response_time = time.time() - start_time
                
                if response.status == 200 and response_time < 30:  # Should respond within 30 seconds
                    self.test_results.append(("Performance", "PASS", f"Response time: {response_time:.2f}s"))
                    print(f"✅ Performance acceptable ({response_time:.2f}s)")
                else:
                    self.test_results.append(("Performance", "FAIL", f"Slow response: {response_time:.2f}s"))
                    print(f"❌ Performance issue: {response_time:.2f}s")
        except Exception as e:
            self.test_results.append(("Performance", "ERROR", str(e)))
            print(f"❌ Performance test error: {e}")
    
    def generate_report(self):
        """Generate final test report"""
        print("\n" + "=" * 60)
        print("📋 PRODUCTION READINESS TEST REPORT")
        print("=" * 60)
        
        passed = sum(1 for _, status, _ in self.test_results if status == "PASS")
        failed = sum(1 for _, status, _ in self.test_results if status == "FAIL")
        errors = sum(1 for _, status, _ in self.test_results if status == "ERROR")
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"🔥 Errors: {errors}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        print("\nDetailed Results:")
        for test_name, status, details in self.test_results:
            status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "🔥"
            print(f"{status_icon} {test_name}: {status} - {details}")
        
        if passed == total:
            print("\n🎉 SYSTEM IS PRODUCTION READY!")
        elif passed >= total * 0.8:
            print("\n⚠️ SYSTEM MOSTLY READY - Minor issues to address")
        else:
            print("\n🚨 SYSTEM NOT READY - Major issues need fixing")
        
        print(f"\nTest completed at: {datetime.now().isoformat()}")

async def main():
    """Run the production readiness test"""
    test_suite = ProductionReadinessTest()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
