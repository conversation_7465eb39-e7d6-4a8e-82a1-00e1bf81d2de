"""
A.T.L.A.S. Enhanced API Manager
Advanced API management and rate limiting using the existing rate limiter
"""

import logging
from typing import Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class AtlasEnhancedAPIManager:
    """
    Enhanced API manager for rate limiting and optimization using existing rate limiter
    """

    def __init__(self):
        self.is_available = True
        self.api_stats = {}
        self.rate_limiter = None
        logger.info("Enhanced API manager initialized")

    async def initialize(self):
        """Initialize the enhanced API manager"""
        try:
            from atlas_rate_limiter import AtlasRateLimiter
            self.rate_limiter = AtlasRateLimiter()
            await self.rate_limiter.initialize()
            logger.info("✅ Enhanced API manager ready")
        except Exception as e:
            logger.error(f"❌ Enhanced API manager initialization failed: {e}")
            self.is_available = False

    async def manage_api_call(self, api_name: str, endpoint: str) -> bool:
        """
        Manage API call with rate limiting
        """
        if not self.rate_limiter:
            return True

        try:
            # Check rate limit
            can_proceed = await self.rate_limiter.check_rate_limit(f"{api_name}_{endpoint}")

            # Update stats
            if api_name not in self.api_stats:
                self.api_stats[api_name] = {
                    'total_calls': 0,
                    'successful_calls': 0,
                    'rate_limited_calls': 0,
                    'last_call': None
                }

            self.api_stats[api_name]['total_calls'] += 1
            self.api_stats[api_name]['last_call'] = datetime.now().isoformat()

            if can_proceed:
                self.api_stats[api_name]['successful_calls'] += 1
            else:
                self.api_stats[api_name]['rate_limited_calls'] += 1

            return can_proceed

        except Exception as e:
            logger.error(f"API call management failed for {api_name}/{endpoint}: {e}")
            return True  # Allow call on error

    def get_api_stats(self) -> Dict[str, Any]:
        """Get API usage statistics"""
        return {
            'api_stats': self.api_stats,
            'total_apis': len(self.api_stats),
            'is_available': self.is_available,
            'timestamp': datetime.now().isoformat()
        }

    def is_manager_available(self) -> bool:
        """Check if enhanced API manager is available"""
        return self.is_available

# Global instance
enhanced_api_manager = AtlasEnhancedAPIManager()

__all__ = ["AtlasEnhancedAPIManager", "enhanced_api_manager"]
