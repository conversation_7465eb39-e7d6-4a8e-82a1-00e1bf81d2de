"""
A.T.L.A.S. Multi-Database Management System
Manages 6 specialized databases for different system components
"""

import asyncio
import logging
import sqlite3
import aiosqlite
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from pathlib import Path
from contextlib import asynccontextmanager

# Core imports
from config import settings
from atlas_secrets_manager import secrets_manager

logger = logging.getLogger(__name__)

@dataclass
class DatabaseInfo:
    """Database information structure"""
    name: str
    path: str
    description: str
    tables: List[str]
    is_connected: bool = False
    last_backup: Optional[datetime] = None
    size_mb: float = 0.0

class AtlasDatabaseManager:
    """
    Multi-database management system for A.T.L.A.S.
    Manages 6 specialized databases with automatic initialization and maintenance
    """
    
    def __init__(self):
        self.databases_dir = Path("databases")
        self.databases_dir.mkdir(exist_ok=True)
        
        # Database configurations
        self.database_configs = {
            "main": {
                "filename": "atlas_main.db",
                "description": "Main trading data and system state",
                "tables": ["positions", "orders", "trades", "system_state", "user_preferences"]
            },
            "market_data": {
                "filename": "atlas_market_data.db", 
                "description": "Real-time and historical market data",
                "tables": ["quotes", "historical_data", "market_indicators", "volume_data"]
            },
            "news_sentiment": {
                "filename": "atlas_news_sentiment.db",
                "description": "News articles and sentiment analysis",
                "tables": ["news_articles", "sentiment_scores", "news_sources", "article_symbols"]
            },
            "patterns_signals": {
                "filename": "atlas_patterns_signals.db",
                "description": "Trading patterns and signals",
                "tables": ["lee_method_signals", "ttm_squeeze_signals", "pattern_detections", "signal_history"]
            },
            "ai_memory": {
                "filename": "atlas_ai_memory.db",
                "description": "AI conversation memory and context",
                "tables": ["conversations", "context_memory", "user_interactions", "ai_responses"]
            },
            "performance": {
                "filename": "atlas_performance.db",
                "description": "System performance and analytics",
                "tables": ["performance_metrics", "system_logs", "error_tracking", "usage_statistics"]
            }
        }
        
        self.connections: Dict[str, aiosqlite.Connection] = {}
        self.database_info: Dict[str, DatabaseInfo] = {}
        
        logger.info("Database manager initialized with 6 specialized databases")
    
    async def initialize_all_databases(self) -> bool:
        """Initialize all 6 databases"""
        try:
            logger.info("🔄 Initializing all A.T.L.A.S. databases...")
            
            success_count = 0
            for db_name, config in self.database_configs.items():
                try:
                    await self.initialize_database(db_name)
                    success_count += 1
                    logger.info(f"✅ Database '{db_name}' initialized successfully")
                except Exception as e:
                    logger.error(f"❌ Failed to initialize database '{db_name}': {e}")
            
            logger.info(f"🎯 Database initialization complete: {success_count}/{len(self.database_configs)} databases ready")
            return success_count == len(self.database_configs)
            
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            return False
    
    async def initialize_database(self, db_name: str) -> bool:
        """Initialize a specific database"""
        try:
            if db_name not in self.database_configs:
                raise ValueError(f"Unknown database: {db_name}")
            
            config = self.database_configs[db_name]
            db_path = self.databases_dir / config["filename"]
            
            # Create connection
            connection = await aiosqlite.connect(str(db_path))
            connection.row_factory = aiosqlite.Row
            self.connections[db_name] = connection
            
            # Create tables
            await self._create_database_tables(db_name, connection)
            
            # Update database info
            self.database_info[db_name] = DatabaseInfo(
                name=db_name,
                path=str(db_path),
                description=config["description"],
                tables=config["tables"],
                is_connected=True,
                size_mb=self._get_database_size(db_path)
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize database {db_name}: {e}")
            return False
    
    async def _create_database_tables(self, db_name: str, connection: aiosqlite.Connection):
        """Create tables for a specific database"""
        try:
            if db_name == "main":
                await self._create_main_tables(connection)
            elif db_name == "market_data":
                await self._create_market_data_tables(connection)
            elif db_name == "news_sentiment":
                await self._create_news_sentiment_tables(connection)
            elif db_name == "patterns_signals":
                await self._create_patterns_signals_tables(connection)
            elif db_name == "ai_memory":
                await self._create_ai_memory_tables(connection)
            elif db_name == "performance":
                await self._create_performance_tables(connection)
                
        except Exception as e:
            logger.error(f"Failed to create tables for {db_name}: {e}")
            raise
    
    async def _create_main_tables(self, conn: aiosqlite.Connection):
        """Create main database tables"""
        tables = [
            """CREATE TABLE IF NOT EXISTS positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                avg_price REAL NOT NULL,
                current_price REAL,
                market_value REAL,
                unrealized_pnl REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                order_type TEXT NOT NULL,
                price REAL,
                status TEXT NOT NULL,
                filled_qty INTEGER DEFAULT 0,
                avg_fill_price REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                price REAL NOT NULL,
                commission REAL DEFAULT 0,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS system_state (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS user_preferences (
                user_id TEXT PRIMARY KEY,
                preferences TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )"""
        ]
        
        for table_sql in tables:
            await conn.execute(table_sql)
        await conn.commit()
    
    async def _create_market_data_tables(self, conn: aiosqlite.Connection):
        """Create market data tables"""
        tables = [
            """CREATE TABLE IF NOT EXISTS quotes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                price REAL NOT NULL,
                bid REAL,
                ask REAL,
                volume INTEGER,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, timestamp)
            )""",
            
            """CREATE TABLE IF NOT EXISTS historical_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                date DATE NOT NULL,
                open_price REAL NOT NULL,
                high_price REAL NOT NULL,
                low_price REAL NOT NULL,
                close_price REAL NOT NULL,
                volume INTEGER NOT NULL,
                UNIQUE(symbol, date)
            )""",
            
            """CREATE TABLE IF NOT EXISTS market_indicators (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                indicator_name TEXT NOT NULL,
                value REAL NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS volume_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                volume INTEGER NOT NULL,
                avg_volume INTEGER,
                volume_ratio REAL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )"""
        ]
        
        for table_sql in tables:
            await conn.execute(table_sql)
        await conn.commit()
    
    async def _create_news_sentiment_tables(self, conn: aiosqlite.Connection):
        """Create news and sentiment tables"""
        tables = [
            """CREATE TABLE IF NOT EXISTS news_articles (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                content TEXT,
                source TEXT NOT NULL,
                url TEXT,
                published_at TIMESTAMP NOT NULL,
                sentiment_score REAL,
                confidence REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS sentiment_scores (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                sentiment_score REAL NOT NULL,
                confidence REAL NOT NULL,
                source TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS news_sources (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_name TEXT UNIQUE NOT NULL,
                reliability_score REAL DEFAULT 0.5,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS article_symbols (
                article_id TEXT NOT NULL,
                symbol TEXT NOT NULL,
                relevance_score REAL DEFAULT 1.0,
                PRIMARY KEY (article_id, symbol),
                FOREIGN KEY (article_id) REFERENCES news_articles(id)
            )"""
        ]
        
        for table_sql in tables:
            await conn.execute(table_sql)
        await conn.commit()
    
    async def _create_patterns_signals_tables(self, conn: aiosqlite.Connection):
        """Create patterns and signals tables"""
        tables = [
            """CREATE TABLE IF NOT EXISTS lee_method_signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                signal_type TEXT NOT NULL,
                strength INTEGER NOT NULL,
                criteria_met INTEGER NOT NULL,
                price REAL NOT NULL,
                volume INTEGER NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS ttm_squeeze_signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                squeeze_status TEXT NOT NULL,
                momentum REAL NOT NULL,
                bb_squeeze BOOLEAN NOT NULL,
                kc_squeeze BOOLEAN NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS pattern_detections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                pattern_type TEXT NOT NULL,
                confidence REAL NOT NULL,
                entry_price REAL,
                target_price REAL,
                stop_loss REAL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS signal_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                signal_source TEXT NOT NULL,
                signal_data TEXT NOT NULL,
                outcome TEXT,
                performance_score REAL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )"""
        ]
        
        for table_sql in tables:
            await conn.execute(table_sql)
        await conn.commit()
    
    async def _create_ai_memory_tables(self, conn: aiosqlite.Connection):
        """Create AI memory tables"""
        tables = [
            """CREATE TABLE IF NOT EXISTS conversations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                conversation_id TEXT NOT NULL,
                message TEXT NOT NULL,
                response TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS context_memory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                context_key TEXT NOT NULL,
                context_value TEXT NOT NULL,
                importance REAL DEFAULT 0.5,
                expires_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS user_interactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                interaction_type TEXT NOT NULL,
                data TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS ai_responses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                query TEXT NOT NULL,
                response TEXT NOT NULL,
                model_used TEXT NOT NULL,
                confidence REAL,
                processing_time REAL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )"""
        ]
        
        for table_sql in tables:
            await conn.execute(table_sql)
        await conn.commit()
    
    async def _create_performance_tables(self, conn: aiosqlite.Connection):
        """Create performance monitoring tables"""
        tables = [
            """CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                metric_name TEXT NOT NULL,
                metric_value REAL NOT NULL,
                category TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                level TEXT NOT NULL,
                message TEXT NOT NULL,
                module TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS error_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                error_type TEXT NOT NULL,
                error_message TEXT NOT NULL,
                stack_trace TEXT,
                module TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS usage_statistics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                feature_name TEXT NOT NULL,
                usage_count INTEGER DEFAULT 1,
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_id TEXT
            )"""
        ]
        
        for table_sql in tables:
            await conn.execute(table_sql)
        await conn.commit()
    
    @asynccontextmanager
    async def get_connection(self, db_name: str):
        """Get database connection context manager"""
        if db_name not in self.connections:
            await self.initialize_database(db_name)
        
        connection = self.connections[db_name]
        try:
            yield connection
        finally:
            # Connection cleanup handled by manager
            pass
    
    async def execute_query(self, db_name: str, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """Execute a query and return results"""
        try:
            async with self.get_connection(db_name) as conn:
                if params:
                    cursor = await conn.execute(query, params)
                else:
                    cursor = await conn.execute(query)
                
                rows = await cursor.fetchall()
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Query execution failed on {db_name}: {e}")
            return []
    
    async def execute_insert(self, db_name: str, query: str, params: tuple = None) -> int:
        """Execute an insert query and return row ID"""
        try:
            async with self.get_connection(db_name) as conn:
                if params:
                    cursor = await conn.execute(query, params)
                else:
                    cursor = await conn.execute(query)
                
                await conn.commit()
                return cursor.lastrowid
                
        except Exception as e:
            logger.error(f"Insert execution failed on {db_name}: {e}")
            return -1
    
    def _get_database_size(self, db_path: Path) -> float:
        """Get database file size in MB"""
        try:
            if db_path.exists():
                size_bytes = db_path.stat().st_size
                return round(size_bytes / (1024 * 1024), 2)
            return 0.0
        except Exception:
            return 0.0
    
    async def get_database_status(self) -> Dict[str, Any]:
        """Get status of all databases"""
        status = {
            "total_databases": len(self.database_configs),
            "connected_databases": len([db for db in self.database_info.values() if db.is_connected]),
            "total_size_mb": sum(db.size_mb for db in self.database_info.values()),
            "databases": {}
        }
        
        for db_name, db_info in self.database_info.items():
            status["databases"][db_name] = {
                "connected": db_info.is_connected,
                "size_mb": db_info.size_mb,
                "description": db_info.description,
                "tables": len(db_info.tables)
            }
        
        return status
    
    async def backup_database(self, db_name: str, backup_dir: str = "backups") -> bool:
        """Backup a specific database"""
        try:
            backup_path = Path(backup_dir)
            backup_path.mkdir(exist_ok=True)
            
            if db_name not in self.database_info:
                return False
            
            db_info = self.database_info[db_name]
            source_path = Path(db_info.path)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{db_name}_{timestamp}.db"
            backup_file = backup_path / backup_filename
            
            # Copy database file
            import shutil
            shutil.copy2(source_path, backup_file)
            
            # Update backup timestamp
            self.database_info[db_name].last_backup = datetime.now()
            
            logger.info(f"Database {db_name} backed up to {backup_file}")
            return True
            
        except Exception as e:
            logger.error(f"Database backup failed for {db_name}: {e}")
            return False
    
    async def cleanup_old_data(self, days_to_keep: int = 30):
        """Clean up old data from all databases"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            cleanup_queries = {
                "market_data": "DELETE FROM quotes WHERE timestamp < ?",
                "news_sentiment": "DELETE FROM news_articles WHERE created_at < ?",
                "patterns_signals": "DELETE FROM signal_history WHERE timestamp < ?",
                "ai_memory": "DELETE FROM conversations WHERE timestamp < ?",
                "performance": "DELETE FROM system_logs WHERE timestamp < ?"
            }
            
            for db_name, query in cleanup_queries.items():
                try:
                    async with self.get_connection(db_name) as conn:
                        await conn.execute(query, (cutoff_date,))
                        await conn.commit()
                    logger.info(f"Cleaned up old data from {db_name}")
                except Exception as e:
                    logger.error(f"Cleanup failed for {db_name}: {e}")
                    
        except Exception as e:
            logger.error(f"Data cleanup failed: {e}")
    
    async def close_all_connections(self):
        """Close all database connections"""
        for db_name, connection in self.connections.items():
            try:
                await connection.close()
                logger.info(f"Closed connection to {db_name}")
            except Exception as e:
                logger.error(f"Error closing connection to {db_name}: {e}")
        
        self.connections.clear()
        for db_info in self.database_info.values():
            db_info.is_connected = False

# Global database manager instance
database_manager = AtlasDatabaseManager()

# Convenience functions
async def get_connection(db_name: str):
    """Get database connection"""
    return database_manager.get_connection(db_name)

async def execute_query(db_name: str, query: str, params: tuple = None) -> List[Dict[str, Any]]:
    """Execute query on specified database"""
    return await database_manager.execute_query(db_name, query, params)

async def execute_insert(db_name: str, query: str, params: tuple = None) -> int:
    """Execute insert on specified database"""
    return await database_manager.execute_insert(db_name, query, params)

# Export main classes and functions
__all__ = [
    "AtlasDatabaseManager",
    "DatabaseInfo",
    "database_manager",
    "get_connection",
    "execute_query",
    "execute_insert"
]
