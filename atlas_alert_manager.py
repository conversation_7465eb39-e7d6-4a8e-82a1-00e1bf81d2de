"""
A.T.L.A.S. Alert Manager
Advanced alert system for trading opportunities and risk warnings
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum

from models import AlertType, AlertPriority

logger = logging.getLogger(__name__)

class AlertCategory(Enum):
    """Alert category enumeration"""
    TRADING_OPPORTUNITY = "trading_opportunity"
    RISK_WARNING = "risk_warning"
    MARKET_UPDATE = "market_update"
    SYSTEM_STATUS = "system_status"
    LEE_METHOD_SIGNAL = "lee_method_signal"
    PORTFOLIO_UPDATE = "portfolio_update"

@dataclass
class Alert:
    """Alert data structure"""
    alert_id: str
    category: AlertCategory
    type: AlertType
    priority: AlertPriority
    title: str
    message: str
    symbol: Optional[str] = None
    data: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    acknowledged: bool = False
    delivered: bool = False

class AtlasAlertManager:
    """Advanced alert management system"""

    def __init__(self):
        self.alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.subscribers: Dict[AlertCategory, List[callable]] = {}
        self.alert_rules: Dict[str, Dict[str, Any]] = {}
        self.cooldown_periods: Dict[str, datetime] = {}
        self.logger = logging.getLogger("atlas.alerts")

        # Initialize default alert rules
        self._setup_default_rules()

    def _setup_default_rules(self):
        """Setup default alert rules"""
        self.alert_rules = {
            "lee_method_signal": {
                "cooldown_minutes": 15,
                "min_confidence": 0.7,
                "max_per_hour": 10
            },
            "risk_warning": {
                "cooldown_minutes": 5,
                "min_severity": "medium",
                "max_per_hour": 20
            },
            "trading_opportunity": {
                "cooldown_minutes": 10,
                "min_strength": 3,
                "max_per_hour": 15
            }
        }

    async def create_alert(
        self,
        category: AlertCategory,
        alert_type: AlertType,
        priority: AlertPriority,
        title: str,
        message: str,
        symbol: Optional[str] = None,
        data: Optional[Dict[str, Any]] = None,
        expires_in_minutes: Optional[int] = None
    ) -> str:
        """Create a new alert"""
        try:
            # Generate unique alert ID
            alert_id = f"{category.value}_{symbol or 'system'}_{int(datetime.now().timestamp())}"

            # Check cooldown
            if not self._check_cooldown(category, symbol):
                self.logger.debug(f"Alert creation blocked by cooldown: {category.value}")
                return ""

            # Set expiration
            expires_at = None
            if expires_in_minutes:
                expires_at = datetime.now() + timedelta(minutes=expires_in_minutes)

            # Create alert
            alert = Alert(
                alert_id=alert_id,
                category=category,
                type=alert_type,
                priority=priority,
                title=title,
                message=message,
                symbol=symbol,
                data=data or {},
                expires_at=expires_at
            )

            # Store alert
            self.alerts[alert_id] = alert
            self.alert_history.append(alert)

            # Update cooldown
            self._update_cooldown(category, symbol)

            # Deliver alert
            await self._deliver_alert(alert)

            self.logger.info(f"Alert created: {alert_id} - {title}")
            return alert_id

        except Exception as e:
            self.logger.error(f"Failed to create alert: {e}")
            return ""

    async def create_trading_opportunity_alert(
        self,
        symbol: str,
        opportunity_type: str,
        strength: int,
        confidence: float,
        entry_price: float,
        target_price: float,
        stop_loss: float,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a trading opportunity alert"""

        title = f"🚀 Trading Opportunity: {symbol}"
        message = f"""
{opportunity_type.upper()} opportunity detected for {symbol}

📊 Signal Strength: {strength}/5 stars
🎯 Confidence: {confidence:.1%}
💰 Entry: ${entry_price:.2f}
🎯 Target: ${target_price:.2f}
🛡️ Stop Loss: ${stop_loss:.2f}

Risk/Reward: {((target_price - entry_price) / (entry_price - stop_loss)):.2f}:1
        """.strip()

        data = {
            "opportunity_type": opportunity_type,
            "strength": strength,
            "confidence": confidence,
            "entry_price": entry_price,
            "target_price": target_price,
            "stop_loss": stop_loss,
            **(additional_data or {})
        }

        priority = AlertPriority.HIGH if strength >= 4 else AlertPriority.MEDIUM

        return await self.create_alert(
            category=AlertCategory.TRADING_OPPORTUNITY,
            alert_type=AlertType.INFO,
            priority=priority,
            title=title,
            message=message,
            symbol=symbol,
            data=data,
            expires_in_minutes=30
        )

    async def create_lee_method_alert(
        self,
        symbol: str,
        signal_type: str,
        criteria_met: int,
        confidence: float,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a Lee Method signal alert"""

        title = f"📈 Lee Method Signal: {symbol}"
        message = f"""
Lee Method {signal_type} signal detected for {symbol}

✅ Criteria Met: {criteria_met}/3
🎯 Confidence: {confidence:.1%}

{self._format_lee_method_details(additional_data or {})}
        """.strip()

        data = {
            "signal_type": signal_type,
            "criteria_met": criteria_met,
            "confidence": confidence,
            **(additional_data or {})
        }

        priority = AlertPriority.HIGH if criteria_met == 3 else AlertPriority.MEDIUM

        return await self.create_alert(
            category=AlertCategory.LEE_METHOD_SIGNAL,
            alert_type=AlertType.INFO,
            priority=priority,
            title=title,
            message=message,
            symbol=symbol,
            data=data,
            expires_in_minutes=20
        )

    async def create_risk_warning_alert(
        self,
        symbol: str,
        risk_type: str,
        severity: str,
        current_risk: float,
        threshold: float,
        recommendation: str,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a risk warning alert"""

        title = f"⚠️ Risk Warning: {symbol}"
        message = f"""
{risk_type} risk warning for {symbol}

🚨 Severity: {severity.upper()}
📊 Current Risk: {current_risk:.1%}
🎯 Threshold: {threshold:.1%}

💡 Recommendation: {recommendation}
        """.strip()

        data = {
            "risk_type": risk_type,
            "severity": severity,
            "current_risk": current_risk,
            "threshold": threshold,
            "recommendation": recommendation,
            **(additional_data or {})
        }

        priority_map = {
            "critical": AlertPriority.URGENT,
            "high": AlertPriority.HIGH,
            "medium": AlertPriority.MEDIUM,
            "low": AlertPriority.LOW
        }
        priority = priority_map.get(severity.lower(), AlertPriority.MEDIUM)

        return await self.create_alert(
            category=AlertCategory.RISK_WARNING,
            alert_type=AlertType.WARNING,
            priority=priority,
            title=title,
            message=message,
            symbol=symbol,
            data=data,
            expires_in_minutes=60
        )

    def _format_lee_method_details(self, data: Dict[str, Any]) -> str:
        """Format Lee Method signal details"""
        details = []

        if "trend_confirmation" in data:
            status = "✅" if data["trend_confirmation"] else "❌"
            details.append(f"{status} Trend Confirmation")

        if "volume_validation" in data:
            status = "✅" if data["volume_validation"] else "❌"
            details.append(f"{status} Volume Validation")

        if "technical_pattern" in data:
            status = "✅" if data["technical_pattern"] else "❌"
            details.append(f"{status} Technical Pattern")

        return "\n".join(details) if details else "Signal details not available"

    def _check_cooldown(self, category: AlertCategory, symbol: Optional[str]) -> bool:
        """Check if alert creation is allowed (not in cooldown)"""
        cooldown_key = f"{category.value}_{symbol or 'system'}"

        if cooldown_key in self.cooldown_periods:
            last_alert = self.cooldown_periods[cooldown_key]
            rule_key = category.value.replace("_", "_")
            cooldown_minutes = self.alert_rules.get(rule_key, {}).get("cooldown_minutes", 5)

            if datetime.now() < last_alert + timedelta(minutes=cooldown_minutes):
                return False

        return True

    def _update_cooldown(self, category: AlertCategory, symbol: Optional[str]):
        """Update cooldown timestamp"""
        cooldown_key = f"{category.value}_{symbol or 'system'}"
        self.cooldown_periods[cooldown_key] = datetime.now()

    async def _deliver_alert(self, alert: Alert):
        """Deliver alert to subscribers"""
        try:
            # Mark as delivered
            alert.delivered = True

            # Log the alert
            self.logger.info(f"Alert delivered: {alert.title}")

            # In a real implementation, this would send to:
            # - WebSocket connections
            # - Email subscribers
            # - SMS services
            # - Push notifications
            # - Slack/Discord webhooks

            # For now, just log the alert details
            self.logger.info(f"Alert Details: {alert.message}")

        except Exception as e:
            self.logger.error(f"Failed to deliver alert {alert.alert_id}: {e}")

    def get_active_alerts(self, category: Optional[AlertCategory] = None) -> List[Alert]:
        """Get active (non-expired, non-acknowledged) alerts"""
        now = datetime.now()
        active_alerts = []

        for alert in self.alerts.values():
            # Skip acknowledged alerts
            if alert.acknowledged:
                continue

            # Skip expired alerts
            if alert.expires_at and now > alert.expires_at:
                continue

            # Filter by category if specified
            if category and alert.category != category:
                continue

            active_alerts.append(alert)

        # Sort by priority and timestamp
        priority_order = {
            AlertPriority.URGENT: 0,
            AlertPriority.HIGH: 1,
            AlertPriority.MEDIUM: 2,
            AlertPriority.LOW: 3
        }

        active_alerts.sort(
            key=lambda a: (priority_order.get(a.priority, 99), a.timestamp),
            reverse=True
        )

        return active_alerts

    def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge an alert"""
        if alert_id in self.alerts:
            self.alerts[alert_id].acknowledged = True
            self.logger.info(f"Alert acknowledged: {alert_id}")
            return True
        return False

    def get_alert_summary(self) -> Dict[str, Any]:
        """Get summary of alert system status"""
        active_alerts = self.get_active_alerts()

        return {
            "total_alerts": len(self.alerts),
            "active_alerts": len(active_alerts),
            "alerts_by_priority": {
                priority.value: len([a for a in active_alerts if a.priority == priority])
                for priority in AlertPriority
            },
            "alerts_by_category": {
                category.value: len([a for a in active_alerts if a.category == category])
                for category in AlertCategory
            },
            "recent_alerts": len([
                a for a in self.alert_history
                if a.timestamp > datetime.now() - timedelta(hours=1)
            ])
        }

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = ["AtlasAlertManager", "Alert", "AlertCategory"]