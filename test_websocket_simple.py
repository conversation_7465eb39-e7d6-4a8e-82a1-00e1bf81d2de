#!/usr/bin/env python3
"""
Simple WebSocket Test
"""

import asyncio
import websockets
import json

async def test_websocket():
    uri = "ws://localhost:8002/ws/scanner"
    
    try:
        print("🔌 Connecting to WebSocket...")
        async with websockets.connect(uri) as websocket:
            print("✅ Connected!")
            
            # Wait for welcome message
            try:
                welcome = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📨 Welcome: {welcome}")
            except asyncio.TimeoutError:
                print("⏰ No welcome message received")
            
            # Send help command
            help_cmd = {"type": "command", "command": "help"}
            await websocket.send(json.dumps(help_cmd))
            print("📤 Sent help command")
            
            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📨 Response: {response}")
            except asyncio.TimeoutError:
                print("⏰ No response received")
                
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_websocket())
