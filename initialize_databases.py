#!/usr/bin/env python3
"""
A.T.L.A.S. Database Initialization Script
Sets up all 6 specialized databases with proper schema and initial data
"""

import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from atlas_database import database_manager, AtlasDatabaseManager
from atlas_utils import ensure_directory, format_timestamp
from config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseInitializer:
    """Database initialization and setup utility"""
    
    def __init__(self):
        self.db_manager = database_manager
        self.initialization_log = []
        
    async def initialize_all_databases(self) -> bool:
        """Initialize all A.T.L.A.S. databases"""
        try:
            print("\n" + "=" * 80)
            print("🚀 A.T.L.A.S. DATABASE INITIALIZATION")
            print("=" * 80)
            
            # Ensure databases directory exists
            ensure_directory("databases")
            ensure_directory("backups")
            
            # Initialize all databases
            success = await self.db_manager.initialize_all_databases()
            
            if success:
                print("\n✅ All databases initialized successfully!")
                
                # Create initial data
                await self._create_initial_data()
                
                # Generate status report
                await self._generate_status_report()
                
                print("\n🎯 Database initialization complete!")
                return True
            else:
                print("\n❌ Database initialization failed!")
                return False
                
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            print(f"\n❌ CRITICAL ERROR: {e}")
            return False
    
    async def _create_initial_data(self):
        """Create initial data in databases"""
        try:
            print("\n📊 Creating initial data...")
            
            # Initialize system state
            await self._initialize_system_state()
            
            # Initialize news sources
            await self._initialize_news_sources()
            
            # Initialize performance metrics
            await self._initialize_performance_metrics()
            
            print("✅ Initial data created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create initial data: {e}")
            print(f"⚠️ Warning: Initial data creation failed: {e}")
    
    async def _initialize_system_state(self):
        """Initialize system state table"""
        try:
            initial_state = [
                ("system_version", "5.0"),
                ("initialization_date", datetime.now().isoformat()),
                ("trading_mode", "PAPER"),
                ("system_status", "INITIALIZED"),
                ("last_startup", datetime.now().isoformat()),
                ("database_version", "1.0")
            ]
            
            for key, value in initial_state:
                await self.db_manager.execute_insert(
                    "main",
                    "INSERT OR REPLACE INTO system_state (key, value) VALUES (?, ?)",
                    (key, value)
                )
            
            logger.info("System state initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize system state: {e}")
    
    async def _initialize_news_sources(self):
        """Initialize news sources table"""
        try:
            news_sources = [
                ("Reuters", 0.9),
                ("Bloomberg", 0.9),
                ("MarketWatch", 0.8),
                ("CNBC", 0.8),
                ("Yahoo Finance", 0.7),
                ("Financial Times", 0.9),
                ("Wall Street Journal", 0.9),
                ("Seeking Alpha", 0.6),
                ("Benzinga", 0.7),
                ("The Motley Fool", 0.6)
            ]
            
            for source_name, reliability in news_sources:
                await self.db_manager.execute_insert(
                    "news_sentiment",
                    "INSERT OR IGNORE INTO news_sources (source_name, reliability_score) VALUES (?, ?)",
                    (source_name, reliability)
                )
            
            logger.info("News sources initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize news sources: {e}")
    
    async def _initialize_performance_metrics(self):
        """Initialize performance metrics"""
        try:
            initial_metrics = [
                ("system_startup_time", 0.0, "system"),
                ("database_connections", 6.0, "database"),
                ("api_response_time", 0.0, "api"),
                ("memory_usage_mb", 0.0, "system"),
                ("cpu_usage_percent", 0.0, "system")
            ]
            
            for metric_name, value, category in initial_metrics:
                await self.db_manager.execute_insert(
                    "performance",
                    "INSERT INTO performance_metrics (metric_name, metric_value, category) VALUES (?, ?, ?)",
                    (metric_name, value, category)
                )
            
            logger.info("Performance metrics initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize performance metrics: {e}")
    
    async def _generate_status_report(self):
        """Generate database status report"""
        try:
            print("\n📋 DATABASE STATUS REPORT")
            print("-" * 50)
            
            status = await self.db_manager.get_database_status()
            
            print(f"Total Databases: {status['total_databases']}")
            print(f"Connected: {status['connected_databases']}")
            print(f"Total Size: {status['total_size_mb']:.2f} MB")
            print()
            
            for db_name, db_info in status["databases"].items():
                status_icon = "✅" if db_info["connected"] else "❌"
                print(f"{status_icon} {db_name.upper()}")
                print(f"   Description: {db_info['description']}")
                print(f"   Tables: {db_info['tables']}")
                print(f"   Size: {db_info['size_mb']:.2f} MB")
                print()
            
        except Exception as e:
            logger.error(f"Failed to generate status report: {e}")
    
    async def verify_database_integrity(self) -> bool:
        """Verify database integrity"""
        try:
            print("\n🔍 Verifying database integrity...")
            
            all_good = True
            
            for db_name in self.db_manager.database_configs.keys():
                try:
                    # Test connection
                    async with self.db_manager.get_connection(db_name) as conn:
                        # Run integrity check
                        cursor = await conn.execute("PRAGMA integrity_check")
                        result = await cursor.fetchone()
                        
                        if result and result[0] == "ok":
                            print(f"✅ {db_name}: Integrity OK")
                        else:
                            print(f"❌ {db_name}: Integrity issues detected")
                            all_good = False
                            
                except Exception as e:
                    print(f"❌ {db_name}: Connection failed - {e}")
                    all_good = False
            
            return all_good
            
        except Exception as e:
            logger.error(f"Database integrity check failed: {e}")
            return False
    
    async def create_sample_data(self):
        """Create sample data for testing"""
        try:
            print("\n🧪 Creating sample data for testing...")
            
            # Sample market data
            sample_quotes = [
                ("AAPL", 150.25, 150.20, 150.30, 1000000),
                ("MSFT", 300.50, 300.45, 300.55, 800000),
                ("GOOGL", 2500.75, 2500.70, 2500.80, 500000),
                ("TSLA", 200.00, 199.95, 200.05, 2000000),
                ("NVDA", 450.25, 450.20, 450.30, 1500000)
            ]
            
            for symbol, price, bid, ask, volume in sample_quotes:
                await self.db_manager.execute_insert(
                    "market_data",
                    "INSERT INTO quotes (symbol, price, bid, ask, volume) VALUES (?, ?, ?, ?, ?)",
                    (symbol, price, bid, ask, volume)
                )
            
            # Sample news article
            await self.db_manager.execute_insert(
                "news_sentiment",
                """INSERT INTO news_articles 
                   (id, title, content, source, url, published_at, sentiment_score, confidence) 
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    "sample_001",
                    "Market Shows Strong Performance",
                    "Markets continue to show resilience with strong trading volumes...",
                    "Reuters",
                    "https://example.com/news/1",
                    datetime.now(),
                    0.7,
                    0.85
                )
            )
            
            # Sample Lee Method signal
            await self.db_manager.execute_insert(
                "patterns_signals",
                "INSERT INTO lee_method_signals (symbol, signal_type, strength, criteria_met, price, volume) VALUES (?, ?, ?, ?, ?, ?)",
                ("AAPL", "bullish", 4, 3, 150.25, 1000000)
            )
            
            print("✅ Sample data created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create sample data: {e}")
            print(f"⚠️ Warning: Sample data creation failed: {e}")
    
    async def backup_databases(self):
        """Create initial backup of all databases"""
        try:
            print("\n💾 Creating initial database backups...")
            
            backup_count = 0
            for db_name in self.db_manager.database_configs.keys():
                if await self.db_manager.backup_database(db_name):
                    backup_count += 1
                    print(f"✅ {db_name} backed up")
                else:
                    print(f"❌ {db_name} backup failed")
            
            print(f"📦 {backup_count} databases backed up successfully")
            
        except Exception as e:
            logger.error(f"Database backup failed: {e}")
            print(f"⚠️ Warning: Database backup failed: {e}")

async def main():
    """Main initialization function"""
    try:
        initializer = DatabaseInitializer()
        
        # Initialize databases
        success = await initializer.initialize_all_databases()
        
        if not success:
            print("\n❌ Database initialization failed!")
            return False
        
        # Verify integrity
        integrity_ok = await initializer.verify_database_integrity()
        
        if not integrity_ok:
            print("\n⚠️ Warning: Database integrity issues detected!")
        
        # Create sample data (optional)
        create_samples = input("\n🧪 Create sample data for testing? (y/N): ").lower().strip()
        if create_samples == 'y':
            await initializer.create_sample_data()
        
        # Create backups
        create_backups = input("\n💾 Create initial backups? (Y/n): ").lower().strip()
        if create_backups != 'n':
            await initializer.backup_databases()
        
        print("\n" + "=" * 80)
        print("🎉 A.T.L.A.S. DATABASE INITIALIZATION COMPLETE!")
        print("=" * 80)
        print("\n📁 Database files created in: ./databases/")
        print("📦 Backup files created in: ./backups/")
        print("\n🚀 Your A.T.L.A.S. system is ready to launch!")
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Initialization cancelled by user")
        return False
    except Exception as e:
        logger.error(f"Initialization failed: {e}")
        print(f"\n❌ CRITICAL ERROR: {e}")
        return False
    finally:
        # Clean up connections
        await database_manager.close_all_connections()

def print_usage():
    """Print usage information"""
    print("""
A.T.L.A.S. Database Initialization Script

Usage:
    python initialize_databases.py [options]

Options:
    --help, -h          Show this help message
    --verify-only       Only verify existing databases
    --backup-only       Only create backups
    --sample-data       Create sample data for testing

Examples:
    python initialize_databases.py                    # Full initialization
    python initialize_databases.py --verify-only     # Verify only
    python initialize_databases.py --sample-data     # Initialize with samples
    """)

if __name__ == "__main__":
    # Handle command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] in ["--help", "-h"]:
            print_usage()
            sys.exit(0)
        elif sys.argv[1] == "--verify-only":
            async def verify_only():
                initializer = DatabaseInitializer()
                return await initializer.verify_database_integrity()
            
            result = asyncio.run(verify_only())
            sys.exit(0 if result else 1)
        elif sys.argv[1] == "--backup-only":
            async def backup_only():
                initializer = DatabaseInitializer()
                await initializer.backup_databases()
            
            asyncio.run(backup_only())
            sys.exit(0)
    
    # Run main initialization
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
