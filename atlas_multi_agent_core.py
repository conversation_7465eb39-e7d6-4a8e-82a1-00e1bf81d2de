"""
A.T.L.A.S. Multi-Agent Core
Core classes and interfaces for the multi-agent trading system
"""

import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

# ============================================================================
# CORE ENUMS AND TYPES
# ============================================================================

class AgentRole(Enum):
    """Agent role enumeration"""
    DATA_VALIDATOR = "data_validator"
    PATTERN_DETECTOR = "pattern_detector"
    ANALYSIS_ENGINE = "analysis_engine"
    RISK_MANAGER = "risk_manager"
    TRADE_EXECUTOR = "trade_executor"
    VALIDATION_SUPERVISOR = "validation_supervisor"

class AgentStatus(Enum):
    """Agent status enumeration"""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    BUSY = "busy"
    ERROR = "error"
    STOPPED = "stopped"

class TaskPriority(Enum):
    """Task priority enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

# ============================================================================
# CORE DATA STRUCTURES
# ============================================================================

@dataclass
class MultiAgentTask:
    """Task structure for multi-agent coordination"""
    task_id: str
    description: str
    priority: TaskPriority
    required_agents: List[AgentRole]
    input_data: Dict[str, Any]
    expected_output: Dict[str, Any]
    timeout_seconds: int = 300
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: str = "pending"
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

@dataclass
class AgentCapabilities:
    """Agent capabilities definition"""
    role: AgentRole
    supported_tasks: List[str]
    max_concurrent_tasks: int = 1
    average_processing_time: float = 30.0
    success_rate: float = 0.95
    dependencies: List[AgentRole] = field(default_factory=list)

# ============================================================================
# BASE AGENT CLASS
# ============================================================================

class AtlasBaseAgent(ABC):
    """Base class for all A.T.L.A.S. agents"""

    def __init__(self, role: AgentRole):
        self.role = role
        self.agent_id = str(uuid.uuid4())
        self.status = AgentStatus.INITIALIZING
        self.capabilities = self._define_capabilities()
        self.current_tasks: Dict[str, MultiAgentTask] = {}
        self.completed_tasks = 0
        self.failed_tasks = 0
        self.logger = logging.getLogger(f"atlas.agent.{role.value}")

    @abstractmethod
    def _define_capabilities(self) -> AgentCapabilities:
        """Define agent capabilities - must be implemented by subclasses"""
        pass

    @abstractmethod
    async def execute_task(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Execute a task - must be implemented by subclasses"""
        pass

    async def initialize(self) -> bool:
        """Initialize the agent"""
        try:
            self.logger.info(f"Initializing {self.role.value} agent")
            await self._initialize_agent()
            self.status = AgentStatus.ACTIVE
            self.logger.info(f"{self.role.value} agent initialized successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize {self.role.value} agent: {e}")
            self.status = AgentStatus.ERROR
            return False

    async def _initialize_agent(self):
        """Agent-specific initialization - can be overridden by subclasses"""
        pass

    async def shutdown(self):
        """Shutdown the agent"""
        try:
            self.logger.info(f"Shutting down {self.role.value} agent")
            await self._shutdown_agent()
            self.status = AgentStatus.STOPPED
        except Exception as e:
            self.logger.error(f"Error shutting down {self.role.value} agent: {e}")

    async def _shutdown_agent(self):
        """Agent-specific shutdown - can be overridden by subclasses"""
        pass

    def get_status(self) -> Dict[str, Any]:
        """Get agent status information"""
        return {
            "agent_id": self.agent_id,
            "role": self.role.value,
            "status": self.status.value,
            "capabilities": {
                "supported_tasks": self.capabilities.supported_tasks,
                "max_concurrent_tasks": self.capabilities.max_concurrent_tasks,
                "average_processing_time": self.capabilities.average_processing_time,
                "success_rate": self.capabilities.success_rate
            },
            "performance": {
                "completed_tasks": self.completed_tasks,
                "failed_tasks": self.failed_tasks,
                "success_rate": self.completed_tasks / max(1, self.completed_tasks + self.failed_tasks),
                "current_tasks": len(self.current_tasks)
            }
        }

# ============================================================================
# MULTI-AGENT COORDINATOR
# ============================================================================

class MultiAgentCoordinator:
    """Coordinator for managing multiple agents"""

    def __init__(self):
        self.agents: Dict[AgentRole, AtlasBaseAgent] = {}
        self.task_queue: List[MultiAgentTask] = []
        self.active_tasks: Dict[str, MultiAgentTask] = {}
        self.completed_tasks: Dict[str, MultiAgentTask] = {}
        self.logger = logging.getLogger("atlas.coordinator")

    async def initialize(self) -> bool:
        """Initialize the coordinator"""
        try:
            self.logger.info("Initializing Multi-Agent Coordinator")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize coordinator: {e}")
            return False

    async def register_agent(self, agent: AtlasBaseAgent) -> bool:
        """Register an agent with the coordinator"""
        try:
            if await agent.initialize():
                self.agents[agent.role] = agent
                self.logger.info(f"Registered {agent.role.value} agent")
                return True
            else:
                self.logger.error(f"Failed to initialize {agent.role.value} agent")
                return False
        except Exception as e:
            self.logger.error(f"Error registering {agent.role.value} agent: {e}")
            return False

    async def unregister_agent(self, role: AgentRole) -> bool:
        """Unregister an agent"""
        try:
            if role in self.agents:
                await self.agents[role].shutdown()
                del self.agents[role]
                self.logger.info(f"Unregistered {role.value} agent")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Error unregistering {role.value} agent: {e}")
            return False

    def get_agent(self, role: AgentRole) -> Optional[AtlasBaseAgent]:
        """Get an agent by role"""
        return self.agents.get(role)

    def get_all_agents(self) -> Dict[AgentRole, AtlasBaseAgent]:
        """Get all registered agents"""
        return self.agents.copy()

    async def execute_task(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Execute a task using appropriate agents"""
        try:
            self.active_tasks[task.task_id] = task
            task.started_at = datetime.now()
            task.status = "running"

            # Execute task with required agents
            results = {}
            for agent_role in task.required_agents:
                if agent_role in self.agents:
                    agent = self.agents[agent_role]
                    if agent.status == AgentStatus.ACTIVE:
                        result = await agent.execute_task(task)
                        results[agent_role.value] = result
                    else:
                        results[agent_role.value] = {"error": f"Agent {agent_role.value} not active"}
                else:
                    results[agent_role.value] = {"error": f"Agent {agent_role.value} not registered"}

            task.completed_at = datetime.now()
            task.status = "completed"
            task.result = results

            # Move to completed tasks
            self.completed_tasks[task.task_id] = task
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]

            return results

        except Exception as e:
            task.status = "failed"
            task.error = str(e)
            self.logger.error(f"Task execution failed: {e}")
            return {"error": str(e)}

    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        return {
            "registered_agents": len(self.agents),
            "active_tasks": len(self.active_tasks),
            "completed_tasks": len(self.completed_tasks),
            "agents": {role.value: agent.get_status() for role, agent in self.agents.items()}
        }

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AgentRole",
    "AgentStatus",
    "TaskPriority",
    "MultiAgentTask",
    "AgentCapabilities",
    "AtlasBaseAgent",
    "MultiAgentCoordinator"
]